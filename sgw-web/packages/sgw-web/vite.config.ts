import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";
import svgrPlugin from "vite-plugin-svgr";
import viteTsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  plugins: [
    react({
      babel: {
        parserOpts: {
          plugins: ["decorators-legacy"]
        },
        plugins: [
          [
            "babel-plugin-styled-components",
            {
              displayName: true,
              fileName: false
            }
          ]
        ]
      }
    }),
    viteTsconfigPaths(),
    svgrPlugin()
  ],
  envDir: "env",
  server: {
    open: true,
    port: 3001,
    proxy: {
      "/draft": "http://localhost:3000",
      "/file": "http://localhost:3000",
      "/my-careers-future": "http://localhost:3000",
      "/ndi": "http://localhost:3000",
      "/sgw": "http://localhost:3000",
      "/ssnet": "http://localhost:3000",
      "/user": "http://localhost:3000",
      "/enquiry": "http://localhost:3000",
      "/sequential": "http://localhost:3000"
    }
  },
  build: {
    outDir: "build",
    assetsDir: "static"
  },
  define: {
    __LAST_UPDATED__: JSON.stringify(
      new Intl.DateTimeFormat("en-GB", {
        day: "numeric",
        month: "short",
        year: "numeric",
        hour: "numeric",
        minute: "numeric",
        second: "numeric",
        hourCycle: "h12",
        timeZone: "Asia/Singapore"
      }).format(new Date())
    )
  }
});
