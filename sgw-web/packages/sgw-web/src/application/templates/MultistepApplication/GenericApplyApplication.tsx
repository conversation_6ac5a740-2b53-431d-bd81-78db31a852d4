import {
  QueryClient,
  QueryClientProvider,
  useQuery
} from "@tanstack/react-query";
import { useState, useMemo } from "react";
import {
  Redirect,
  Route,
  Switch,
  useHistory,
  useParams
} from "react-router-dom";

import { getDraft } from "application/api/draft";
import { ApplicationState, SgwAppStatus, ApplyInfo } from "application/api/sgw";
import { ApplicationPage } from "application/composites/ApplicationPage";
import { FormMetadataProvider } from "application/context/FormMetadata";
import { parseSchema } from "application/utils/formTriggers";
import { Spinner } from "commons/components/Spinner";

import { ApplicationSchema } from "./GenericApplication.helper";
import { appPath, useAppStatusQuery, useAppSchemaQuery } from "./helper";
import { ApplyDashboard } from "./AppDashboard";
import { ApplyForm } from "application/templates/MultistepApplication/ApplyForm";

interface ApplicationProps {
  applications: SgwAppStatus[];
  applicationSchema: ApplicationSchema;
  applicationState: ApplicationState;
  maintenanceMsg?: string;
  ineligibleCode?: string;
  applyInfo?: ApplyInfo;
  agencyUnavailable?: boolean;
}

export const ApplyApplication = ({
  applications,
  applicationSchema,
  applicationState,
  maintenanceMsg,
  ineligibleCode,
  applyInfo,
  agencyUnavailable
}: ApplicationProps) => {
  const schemeCode = applicationSchema.schemeCode;
  const path = appPath(schemeCode);
  const { push } = useHistory();
  const latestApplication = applications[0];

  const [selectedSchema, setSelectedSchema] = useState(
    applicationSchema.schema.length === 1
      ? applicationSchema.schema[0]
      : undefined
  );

  const triggerMap = useMemo(() => {
    return parseSchema(selectedSchema);
  }, [selectedSchema]);

  const { data: draft, isLoading } = useQuery(
    ["draft", schemeCode],
    () => getDraft(schemeCode),
    { enabled: applicationState === "allow" }
  );

  const ineligibleReason = ineligibleCode
    ? applicationSchema.ineligibleCodeToReason?.[ineligibleCode]
    : undefined;

  return (
    <FormMetadataProvider
      draft={draft}
      fileUploadExternal={applicationSchema.fileUploadExternal}
    >
      <Switch>
        <Route exact path={path.applyDashboard}>
          <ApplyDashboard
            schemeCode={schemeCode}
            application={latestApplication}
            applicationState={applicationState}
            dashboard={applicationSchema.dashboard}
            contacts={applicationSchema.contacts}
            ineligibleReason={ineligibleReason}
            applicationJourney={applicationSchema.applicationJourney}
            applicationSchema={applicationSchema.schema}
            onSelectedSchemaId={(id) => {
              const schema = applicationSchema.schema.find(
                (schema) => schema.id === id
              );
              if (schema) {
                setSelectedSchema(schema);
              }
              push(path.overview);
            }}
            maintenanceMsg={maintenanceMsg}
            bundleSchemeCode={applicationSchema.bundleSchemeCode}
            applyInfo={applyInfo}
            agencyUnavailable={agencyUnavailable}
          />
        </Route>

        {applicationState !== "allow" && (
          <Route>
            <Redirect to={path.applyDashboard} />
          </Route>
        )}

        <Route path={path.overview}>
          {selectedSchema ? (
            isLoading ? (
              <Spinner />
            ) : (
              <ApplyForm
                applicationSchema={applicationSchema}
                schema={selectedSchema}
                draft={draft?.content}
                triggerMap={triggerMap}
              />
            )
          ) : (
            <Redirect to={path.applyDashboard} />
          )}
        </Route>

        <Route>
          <Redirect to={path.notFound} />
        </Route>
      </Switch>
    </FormMetadataProvider>
  );
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
      refetchOnReconnect: false,
      networkMode: "always"
    },
    mutations: {
      retry: false,
      networkMode: "always"
    }
  }
});

const GenericApplyApplication = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ApplicationGenerator />
    </QueryClientProvider>
  );
};

const ApplicationGenerator = () => {
  const { schemeCode } = useParams<{ schemeCode: string }>();
  const path = appPath(schemeCode);

  const { appSchema, isFetchingAppSchema } = useAppSchemaQuery(schemeCode);

  const { isLoading: isLoadingAppStatus, data: appStatus } = useAppStatusQuery(
    schemeCode
  );
  const {
    status: applications = [],
    applicationState = "allow",
    maintenanceMsg,
    ineligibleCode,
    applyInfo,
    agencyUnavailable = false
  } = appStatus || {};

  if (isFetchingAppSchema) {
    return <Spinner />;
  }

  if (!appSchema) {
    return <Redirect to={path.notFound} />;
  }

  return (
    <ApplicationPage
      title={appSchema.schemeName}
      subtitle={appSchema.subtitle}
      buttonText={appSchema.schemeDetailsLink ? "Scheme details" : "Home"}
      onBackButtonClick={() =>
        (window.location.href = appSchema.schemeDetailsLink || "/")
      }
    >
      {isLoadingAppStatus ? (
        <Spinner />
      ) : (
        <ApplyApplication
          applications={applications}
          applicationSchema={appSchema}
          applicationState={applicationState}
          maintenanceMsg={maintenanceMsg}
          ineligibleCode={ineligibleCode}
          applyInfo={applyInfo}
          agencyUnavailable={agencyUnavailable}
        />
      )}
    </ApplicationPage>
  );
};

export default GenericApplyApplication;
