import axios from "axios";

import { appendCSRF } from "./helper";

export enum FileScanStep {
  APPLICATION = "application",
  OUTSTANDING_DOCUMENTS = "outstanding-documents"
}

export interface FileScanRequest {
  contentType: string;
  fileName: string;
  file: string;
  step: string;
  attachmentType: string;
  fileId?: string; // unique file id for external file upload system to track the file
}

export interface FileScanResponse {
  secure?: boolean;
  isSizeValid: boolean;
  isTypeValid: boolean;
}

const getFileRequestPayload = async (
  file: File,
  step: FileScanStep,
  attachmentType: string,
  fileId?: string
): Promise<FileScanRequest> => {
  const fileInBase64 = await toBase64(file);
  return {
    contentType: file.type,
    fileName: file.name,
    file: fileInBase64,
    step,
    attachmentType,
    fileId
  };
};

export const fileScan = async (
  file: File,
  schemeCode: string,
  step: FileScanStep,
  attachmentType: string,
  abortController?: AbortController
): Promise<FileScanResponse | undefined> => {
  try {
    const payload = await getFileRequestPayload(file, step, attachmentType);
    const res = await axios.post<FileScanResponse>(
      `/file/scan/${schemeCode}`,
      payload,
      { ...appendCSRF(), signal: abortController?.signal, timeout: 30000 }
    );
    return res.data;
  } catch (error) {
    console.warn("error on file scanning", error);
  }

  return undefined;
};

// hash a string using SHA-256 via SubtleCrypto API, return a hashed string in hex format
// reference: https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/digest#converting_a_digest_to_a_hex_string
const digestMessage = async (message: string) => {
  const msgUint8 = new TextEncoder().encode(message); // encode as (utf-8) Uint8Array
  const hashBuffer = await window.crypto.subtle.digest("SHA-256", msgUint8); // hash the message
  const hashArray = Array.from(new Uint8Array(hashBuffer)); // convert buffer to byte array
  return hashArray.map((b) => b.toString(16).padStart(2, "0")).join(""); // convert bytes to hex string
};

export const fileScanExternal = async (
  url: string,
  fileId: string,
  file: File,
  schemeCode: string,
  step: FileScanStep,
  attachmentType: string,
  abortController?: AbortController
): Promise<FileScanResponse | undefined> => {
  try {
    const payload = await getFileRequestPayload(
      file,
      step,
      attachmentType,
      fileId
    );

    const hashedPayload = await digestMessage(JSON.stringify(payload));

    // get token for external file upload system's authorization
    const {
      data: { token }
    } = await axios.post<{ token: string }>(
      `/sgw/${schemeCode}/external/auth`,
      { data: hashedPayload },
      {
        ...appendCSRF(),
        signal: abortController?.signal
      }
    );
    if (!token) {
      throw new Error("No token received for external file scan");
    }

    const res = await axios.post<FileScanResponse>(url, payload, {
      headers: { "x-ccube-token": token }, // this is header token required for sequential file upload
      signal: abortController?.signal,
      timeout: 30000
    });
    return res.data;
  } catch (error) {
    console.warn("error on file scanning external", error);
  }
  return undefined;
};

const toBase64 = (file: Blob): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
