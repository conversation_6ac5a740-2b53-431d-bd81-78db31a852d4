You are a techlead. You will help me, a junior developer, review my raw ideas/proposals on how we can have Sequential schema used/created more effectively and generate enhancement proposal based on a template. I will give you the context to do this task.

**_Background knowledge_**
Sequential is a configuration based platform that enables services to craft a series of actions that run in sequence, and can co-exist within their own workflow.

In other words, a predefined configuration has to be specified in Sequential for a flow to execute. The configuration holds the truth to the action type, whether authentication is required, the redirect information after the flow ends or fails or is cancelled, and even how long a session should exist.

Sequential stores the data collected from each of these actions and hands them back to the owner (or termed as Requestor). The data will be stored for a duration specified by the Requestor and is purged once the duration has passed.

Sequential is:

- A platform offering login methods via various authentication providers for its actions.
- Part of an e-service
- Able to store the data collected through the process for a specified duration
- Stateless (single session approach)

In simple terms, Sequential is a configuration-based form flow creation SASS. Each flow is represented by a service, and we will configure the flow through service config.

**_Current situation_**
We have a demo flow already set up, you can look into sgw/database/seq-schema. The JSON files, ihl-tst-v1.0.0.json under /service and /serviceConfig folders, are the service and service config for the demo flow. Pay attention to the ihl-tst-v1.0.0.json under /serviceConfig folder. We can see different configurations, e.g actionConfig, copyConfig,... but take note of flowActions. Each flow action can be considered a single step in the form flow.
These files were generated manually. You can see that the method of manually creating JSON schemas poses several problems, including potential duplication as well as prone to errors since the configuration could become very long (can span thousands or even hundred thousands of lines). In the future, we will also need to onboard multiple other form flows, therefore it is necessary to have a better solution.

**_My thoughts_**
We can create a script(s) to generate JSON schemas programmatically, instead of manually creating the JSON schemas. This will help us to avoid duplication and errors.
For each form flow, we will have:

- service-config.ts: A typescript service config schema. This will contain the schema for the whole service config.
- << form-name >>.<< flow-action-key >>.ts: We will separate each flow action into a separate typescript file. This will make it easier for developers to collaborate to work on the same form, as well as make it easier to test when developing locally, since it is possible to update service config for a single flow action only. These will then be imported to service-config.ts
- generate-json.ts: Script to generate JSON file. Input shall be what we want to output (a single flow action, or the whole service config). We will then import this script to different service-config.ts files, take schema as input to generate JSON file. Generated forms will be output to /<< environment-name >>/<< version >>/<< form-name >>, for i.e: /dev/0.0.1/ihl-service-config.json. This is for development purpose?

**_Your task_**

1. Review my thoughts, make questions on any unclear points or point out any potential problems you can see.
2. Based on my thoughts, generate a enhancement proposal based on the template provided. The template can be found at sgw/.github/templates/enhancement-proposal/template.md
