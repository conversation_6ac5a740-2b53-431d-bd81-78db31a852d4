# EP-P0001: Sequential JSON Schema Generator

- [Summary](#summary)
- [Motivation](#motivation)
	- [Goals](#goals)
	- [Non-Goals](#non-goals)
- [Proposal](#proposal)
	- [Acceptance criteria](#acceptance-criteria)
		- [AC 1: TypeScript-based Schema Definition](#ac-1-typescript-based-schema-definition)
		- [AC 2: Automated JSON Generation](#ac-2-automated-json-generation)
		- [AC 3: Modular Flow Action Management](#ac-3-modular-flow-action-management)
	- [Notes/Constraints/Caveats](#notesconstraintscaveats)
	- [Risks and Mitigation](#risks-and-mitigation)
- [Design Details](#design-details)
- [Alternatives](#alternatives)
- [Infrastructure Needed](#infrastructure-needed)

## Summary

This proposal introduces a TypeScript-based schema generation system for 
Sequential JSON configurations. Instead of manually creating and maintaining 
large JSON files (potentially thousands of lines), developers will define 
schemas using TypeScript interfaces and generate JSON configurations 
programmatically. This approach reduces duplication, minimizes errors, and 
improves maintainability for Sequential form flow configurations.

## Motivation

Currently, Sequential service configurations are created manually as JSON files.
The existing demo flow (`ihl-tst-v1.0.0.json`) demonstrates the complexity - 
a single configuration file contains 6,684 lines of JSON. This manual approach 
presents several challenges:

1. **Error-prone**: Large JSON files are difficult to validate and debug
2. **Duplication**: Similar flow actions require copy-pasting large blocks
3. **Collaboration difficulties**: Multiple developers cannot easily work on 
   the same configuration simultaneously
4. **Maintenance overhead**: Updates to common patterns require changes across 
   multiple locations
5. **Scalability concerns**: As we onboard more form flows, manual creation 
   becomes unsustainable

### Goals

- Eliminate manual JSON schema creation for Sequential configurations
- Reduce code duplication through reusable TypeScript components
- Enable collaborative development through modular flow action files
- Provide type safety and validation for Sequential schema generation
- Support multiple environments and versioning strategies
- Maintain backward compatibility with existing Sequential platform

### Non-Goals

- Modifying the Sequential platform itself
- Creating a visual schema builder interface
- Replacing existing manually-created schemas immediately (migration will be 
  gradual)
- Supporting non-Sequential form flow platforms

## Proposal

Create a TypeScript-based schema generation system within the existing 
`@sgw/sequential` package that allows developers to define Sequential 
configurations programmatically and generate JSON files automatically.

### Acceptance criteria

#### AC 1: TypeScript-based Schema Definition

- Developers can define service configurations using TypeScript interfaces
- Strong typing ensures schema validity at compile time
- Reusable components for common UI elements (text fields, selects, sections)
- Environment-specific configuration support (dev/staging/prod)

#### AC 2: Automated JSON Generation

- Single command generates JSON files from TypeScript definitions
- Output organized by environment and version (`/dev/v1.0.0/service-name.json`)
- Generated JSON validates against Sequential platform requirements
- Build process integration for CI/CD pipelines

#### AC 3: Modular Flow Action Management

- Each flow action defined in separate TypeScript file
- Flow actions can be developed and tested independently
- Easy composition of flow actions into complete service configurations
- Version control friendly (smaller, focused files)

### Notes/Constraints/Caveats

- Generated schemas must maintain 100% compatibility with Sequential platform
- Migration from existing manual schemas will require careful validation
- TypeScript interfaces must evolve alongside Sequential platform updates
- Generated files should not be manually edited (source of truth is TypeScript)

### Risks and Mitigation

**Risk**: Generated schemas incompatible with Sequential platform
**Mitigation**: Comprehensive validation and testing against known working 
schemas

**Risk**: Learning curve for developers unfamiliar with TypeScript
**Mitigation**: Provide extensive documentation, examples, and templates

**Risk**: Over-engineering leading to unnecessary complexity
**Mitigation**: Start with simple use cases and iterate based on feedback

**Risk**: Breaking changes in Sequential platform affecting generated schemas
**Mitigation**: Version TypeScript interfaces and maintain backward 
compatibility layers

## Design Details

### File Structure
```
sgw/packages/sequential/src/
├── types/                    # TypeScript interfaces
│   ├── sequential.ts         # Core Sequential types
│   ├── flow-actions.ts       # Flow action types
│   └── ui-components.ts      # UI component types
├── builders/                 # Schema builder utilities
│   ├── service-config.ts     # Service config builder
│   ├── flow-action.ts        # Flow action builder
│   └── ui-components.ts      # UI component builders
├── templates/                # Reusable templates
│   ├── common-sections.ts    # Common form sections
│   └── validation-rules.ts   # Common validation patterns
├── generators/               # JSON generation logic
│   ├── generate-json.ts      # Main generation script
│   └── validator.ts          # Schema validation
└── examples/                 # Example implementations
    ├── ihl-service/          # IHL service example
    │   ├── service-config.ts
    │   ├── dashboard.ts
    │   └── profile.ts
    └── simple-form/          # Simple form example
```

### Core TypeScript Interfaces
```typescript
interface SequentialServiceConfig {
  actionConfig: ActionConfig;
  copyConfig: CopyConfig;
  behaviourConfig: BehaviourConfig;
  sessionDuration: number;
  sessionDataLifetime: number;
  flowActions: FlowAction[];
}

interface FlowAction {
  type: 'ui' | 'api' | 'webhook';
  name: string;
  key: string;
  hasAuthRequirement: boolean;
  runConditions: any;
  schema: UISchema[];
}
```

### Generation Workflow
1. Developer defines service configuration in TypeScript
2. Individual flow actions created as separate TypeScript modules
3. Build script composes flow actions into complete service configuration
4. JSON files generated for specified environments and versions
5. Validation ensures generated JSON meets Sequential requirements

## Alternatives

**Alternative 1**: Continue manual JSON creation
- Pros: No development overhead, immediate use
- Cons: Scalability issues, error-prone, difficult collaboration

**Alternative 2**: Visual schema builder
- Pros: User-friendly interface, no coding required
- Cons: Complex to build, limited flexibility, additional maintenance

**Alternative 3**: YAML-based configuration
- Pros: More readable than JSON, supports comments
- Cons: Additional parsing layer, less type safety than TypeScript

## Infrastructure Needed

- Extension of existing `@sgw/sequential` package
- Build scripts integration in CI/CD pipeline
- Documentation hosting for developer guides and examples
- Validation service for generated schemas (optional)
