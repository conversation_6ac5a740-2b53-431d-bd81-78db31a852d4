#!/usr/bin/env ts-node

import * as fs from "fs";
import * as path from "path";

/**
 * Sequential JSON Schema Writer
 * Simple utility for writing JSON schema files for Sequential configuration-based form flows
 */

interface WriteJsonOptions {
  outputPath: string;
  content: object;
}

class SequentialSchemaWriter {
  /**
   * Write JSON content to a file (simplified version for Sequential schemas)
   */
  static writeJsonFile(options: WriteJsonOptions): void {
    const { outputPath, content } = options;

    try {
      // Create directories if they don't exist
      const directory = path.dirname(outputPath);
      if (!fs.existsSync(directory)) {
        fs.mkdirSync(directory, { recursive: true });
        console.log(`📁 Created directory: ${directory}`);
      }

      // Write formatted JSON
      const jsonContent = JSON.stringify(content, null, 2);
      fs.writeFileSync(outputPath, jsonContent, "utf8");
      
      console.log(`✅ Sequential schema written: ${outputPath}`);
      console.log(`📊 Schema size: ${jsonContent.length} characters`);

    } catch (error) {
      console.error(`❌ Error writing schema: ${error}`);
      throw error;
    }
  }

  /**
   * Create a basic Sequential service schema template
   */
  static createBasicServiceSchema(serviceName: string): object {
    return {
      serviceName,
      version: "1.0.0",
      description: `Sequential schema for ${serviceName}`,
      flowActions: [
        {
          type: "ui",
          name: `${serviceName} Form`,
          key: "main-form",
          hasAuthRequirement: true,
          runConditions: null,
          schema: [
            {
              title: serviceName,
              description: `Configuration for ${serviceName}`,
              schema: {
                defaultValues: {},
                sections: {
                  mainSection: {
                    uiType: "section",
                    layoutType: "grid",
                    children: {
                      // Add form fields here
                    }
                  }
                }
              }
            }
          ]
        }
      ]
    };
  }
}

// Example usage
async function main() {
  try {
    console.log("🚀 Sequential Schema Writer - Simple Example");

    // Example 1: Write a simple JSON object
    const simpleSchema = {
      name: "Test Service",
      type: "sequential-form",
      fields: ["field1", "field2", "field3"]
    };

    SequentialSchemaWriter.writeJsonFile({
      outputPath: "./output/simple-schema.json",
      content: simpleSchema
    });

    // Example 2: Create a basic service schema
    const serviceSchema = SequentialSchemaWriter.createBasicServiceSchema("Sample Service");
    
    SequentialSchemaWriter.writeJsonFile({
      outputPath: "./output/sample-service-schema.json",
      content: serviceSchema
    });

    console.log("🎉 Schema generation completed!");

  } catch (error) {
    console.error("💥 Schema generation failed:", error);
    process.exit(1);
  }
}

// Export for use as a module
export { SequentialSchemaWriter };
export type { WriteJsonOptions };

// Run main function if this script is executed directly
if (require.main === module) {
  main();
}
