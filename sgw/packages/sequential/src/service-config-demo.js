import * as fs from "fs";
import * as path from "path";
import { LoadingModalConfig } from "../data";

const serviceConfig = () => ({
  serviceConfigName: "preschool-subsidy-add-docs-v0.0.1", // NOTE: update this to be unique
  actionConfig: {
    success: {
      redirectUrl: `${global.SEQUENTIAL_CONFIG_ESERVICE_BASE_URL}/preschool/form-1-preschool/submission?flowType=docs`,
      webhook: `${global.SEQUENTIAL_CONFIG_BASE_URL}/preschool-bbai-v2/api/preschool-subsidy/sequential/additional-docs/success/v1`,
    },
    cancel: {
      redirectUrl: `${global.SEQUENTIAL_CONFIG_BASE_URL}/services/form-1-preschool`,
    },
    failure: {
      redirectUrl: `${global.SEQUENTIAL_CONFIG_ESERVICE_BASE_URL}/preschool/error/500`,
      webhook: `${global.SEQUENTIAL_CONFIG_BASE_URL}/preschool-bbai-v2/api/preschool-subsidy/sequential/additional-docs/failure/v1`,
    },
    logout: {
      redirectUrl: `${global.SEQUENTIAL_CONFIG_BASE_URL}/services/form-1-preschool`,
    },
  },
  sessionDuration: 21600,
  sessionDataLifetime: 365,
  authRequirement: { SINGPASS: { enabled: true, minLevel: 1 }, GUEST: { enabled: true } },
  progressIndicators: [
    { label: "Required details", section: "details" },
    { label: "Review", section: "review" },
  ],
  behaviourConfig: {
    pageActionConfig: {
      enableCancel: false,
      enableBack: true,
      enableIfNoActionToGoBack: true,
    },
  },
  copyConfig: {
    backButton: {
      label: "Back",
      showIcon: true,
    },
    nextButton: {
      label: "Save and continue",
      showIcon: true,
    },
  },
  flowActions: [
    {
      type: "ui",
      name: "Additional docs",
      key: "additional-details",
      schema: require("./ui-schema/B-details-schema.ts").default,
      schemaConfig: require("./ui-schema/B-details-schema-config.ts").default,
      apiConfig: [
        {
          webhook: `${global.SEQUENTIAL_CONFIG_BASE_URL}/preschool-bbai-v2/api/preschool-subsidy/sequential/additional-docs/supporting-docs-validation/v1`,
          includeSessionData: true,
          includeSessionExtraData: true,
          includeUserData: true,
        },
      ],
      hasAuthRequirement: true,
      actionCopyConfig: {
        pageTitle: "Required details | Preschool enrolment & subsidy – LifeSG",
        loadingModal: LoadingModalConfig,
      },
    },
    {
      type: "review",
      name: "Review",
      key: "review",
      config: require("./ui-schema/B-review-config.ts").default,
      hasAuthRequirement: true,
      actionCopyConfig: {
        pageTitle: "Review | Preschool enrolment & subsidy – LifeSG",
        loadingModal: LoadingModalConfig,
      },
    },
  ],
});

export const generateAdditionalDocsServiceConfig = () => {
  console.log("Generating additonal docs service config");
  fs.writeFileSync(
    path.join(__dirname, "../service-configs/add-docs-service-config.json"),
    JSON.stringify(serviceConfig(), null, 2),
  );
};
