#!/usr/bin/env ts-node

import * as fs from "fs";
import * as path from "path";

/**
 * File Writer Template Script
 * A reusable template for writing various types of content to files
 * Useful for generating service configurations, schemas, and other structured data
 */

interface FileWriterOptions {
  outputPath: string;
  content: string | object;
  encoding?: BufferEncoding;
  createDirectories?: boolean;
  backupExisting?: boolean;
  formatJson?: boolean;
}

class FileWriter {
  /**
   * Write content to a file with various options
   */
  static async writeFile(options: FileWriterOptions): Promise<void> {
    const {
      outputPath,
      content,
      encoding = "utf8",
      createDirectories = true,
      backupExisting = false,
      formatJson = true,
    } = options;

    try {
      // Create directories if they don't exist
      if (createDirectories) {
        const directory = path.dirname(outputPath);
        if (!fs.existsSync(directory)) {
          fs.mkdirSync(directory, { recursive: true });
          console.log(`✅ Created directory: ${directory}`);
        }
      }

      // Backup existing file if requested
      if (backupExisting && fs.existsSync(outputPath)) {
        const backupPath = `${outputPath}.backup.${Date.now()}`;
        fs.copyFileSync(outputPath, backupPath);
        console.log(`💾 Backed up existing file to: ${backupPath}`);
      }

      // Prepare content for writing
      let fileContent: string;
      if (typeof content === "object") {
        fileContent = formatJson ? JSON.stringify(content, null, 2) : JSON.stringify(content);
      } else {
        fileContent = content;
      }

      // Write the file
      fs.writeFileSync(outputPath, fileContent, { encoding });
      console.log(`✅ Successfully wrote file: ${outputPath}`);
      console.log(`📝 Content length: ${fileContent.length} characters`);
    } catch (error) {
      console.error(`❌ Error writing file: ${error}`);
      throw error;
    }
  }

  /**
   * Generate a service configuration template
   */
  static generateServiceConfigTemplate(serviceName: string, _version: string = "v1.0.0"): object {
    return {
      flowActions: [
        {
          type: "ui",
          name: `${serviceName} Dashboard`,
          key: "dashboard",
          hasAuthRequirement: true,
          runConditions: null,
          schema: [
            {
              title: serviceName,
              description: `Configuration for ${serviceName}`,
              schema: {
                defaultValues: {
                  myInfoUsed: true,
                },
                sections: {
                  section: {
                    uiType: "section",
                    layoutType: "grid",
                    children: {
                      header: {},
                      // Add your form fields here
                    },
                  },
                },
              },
            },
          ],
        },
      ],
    };
  }

  /**
   * Utility method to read existing file content
   */
  static readFile(filePath: string, encoding: BufferEncoding = "utf8"): string {
    try {
      return fs.readFileSync(filePath, { encoding });
    } catch (error) {
      console.error(`❌ Error reading file: ${error}`);
      throw error;
    }
  }

  /**
   * Utility method to check if file exists
   */
  static fileExists(filePath: string): boolean {
    return fs.existsSync(filePath);
  }
}

// Example usage and main execution
async function main() {
  try {
    // Example 1: Write a simple text file
    console.log("📄 Example 1: Writing a simple text file...");
    await FileWriter.writeFile({
      outputPath: "./example-output.txt",
      content: "Hello, this is a test file created by the file writer template!",
      createDirectories: true,
    });

    // Example 2: Write a JSON configuration file
    console.log("\n📄 Example 2: Writing a JSON configuration file...");
    const sampleConfig = {
      serviceName: "Sample Service",
      version: "v1.0.0",
      description: "This is a sample configuration",
      settings: {
        enabled: true,
        maxRetries: 3,
        timeout: 5000,
      },
    };

    await FileWriter.writeFile({
      outputPath: "./serviceConfig/sample-config-v1.0.0.json",
      content: sampleConfig,
      createDirectories: true,
      backupExisting: true,
      formatJson: true,
    });

    // Example 3: Generate a service configuration template
    console.log("\n📄 Example 3: Generating service configuration template...");
    const serviceTemplate = FileWriter.generateServiceConfigTemplate("New Service Template");

    await FileWriter.writeFile({
      outputPath: "./serviceConfig/new-service-template-v1.0.0.json",
      content: serviceTemplate,
      createDirectories: true,
      formatJson: true,
    });

    console.log("\n🎉 All examples completed successfully!");
  } catch (error) {
    console.error("💥 Script execution failed:", error);
    process.exit(1);
  }
}

// Export for use as a module
export { FileWriter };
export type { FileWriterOptions };

// Run main function if this script is executed directly
if (require.main === module) {
  main();
}
