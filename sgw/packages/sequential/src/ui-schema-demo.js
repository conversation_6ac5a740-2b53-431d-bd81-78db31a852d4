import dedent from "dedent";

export default [
  {
    title: "Required details",
    description: "Please provide the requested details for your application.",
    progressIndicator: { section: "details" },
    schema: {
      sections: {
        hidden: {
          uiType: "section",
          children: {
            hiddenSection: {
              uiType: "div",
              style: { display: "none" },
              children: {},
            },
          },
        },
        ecdaRemarksSection: {
          uiType: "section",
          layoutType: "contain",
          children: {
            ecdaRemarksWrapper: {
              uiType: "grid",
              showIf: [{ remarksRequiredTrue: [{ shown: true }] }],
              style: { marginBottom: "2rem" },
              children: {
                ecdaRemarks: {
                  uiType: "alert",
                  type: "info",
                  showIcon: true,
                  children: "<strong>Message from ECDA:</strong>",
                  columns: { desktop: 8, tablet: 8, mobile: 4 },
                },
              },
            },
          },
        },
        remarksSection: {
          uiType: "section",
          layoutType: "contain",
          children: {
            remarksWrapper: {
              uiType: "grid",
              showIf: [{ remarksRequiredTrue: [{ shown: true }] }],
              style: { marginBottom: "2rem" },
              children: {
                remarks: {
                  uiType: "textarea",
                  label: {
                    mainLabel: "Please reply with the required details",
                    hint: {
                      content:
                        "Please only provide the additional details that ECDA has requested in order to complete your application.",
                    },
                  },
                  placeholder: "Example: Other parent’s date of birth is 20/12/2000.",
                  validation: [
                    {
                      required: true,
                      errorMessage: "Please enter details",
                    },
                    { max: 1000 },
                  ],
                  resizable: true,
                  rows: 5,
                  columns: { desktop: 8, tablet: 8, mobile: 4 },
                },
              },
            },
          },
        },
        supportingDocsAlertSection: {
          uiType: "section",
          layoutType: "contain",
          children: {
            supportingDocsAlertWrapper: {
              uiType: "grid",
              style: { marginBottom: "2rem" },
              children: {
                supportingDocsAlert: {
                  uiType: "alert",
                  type: "warning",
                  children: dedent`
          <strong>Please upload these supporting documents</strong>
          

          File types: .JPG, .JPEG, .PDF, .PNG
          

          Maximum size per file: 2 MB
         `,
                  columns: { desktop: 8, tablet: 8, mobile: 4 },
                },
              },
            },
          },
        },
        supportingDocsRequiredSection: {
          uiType: "section",
          layoutType: "contain",
          children: {
            fileUploadWrapper: {
              uiType: "grid",
              children: {},
            },
          },
        },
      },
    },
  },
];
