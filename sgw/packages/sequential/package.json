{"name": "@sgw/sequential", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -b .", "clean": "rimraf dist *.tsbuildinfo", "lint": "eslint", "format": "prettier --write \"src/**/*.ts\"", "start": "ts-node src/file-writer-template.ts"}, "dependencies": {"@sgw/common": "0.0.1"}, "devDependencies": {"@types/node": "^18.0.0", "ts-node": "^10.0.0", "typescript": "^4.0.0"}}