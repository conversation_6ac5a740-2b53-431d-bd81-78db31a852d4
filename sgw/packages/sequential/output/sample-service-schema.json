{"serviceName": "Sample Service", "version": "1.0.0", "description": "Sequential schema for Sample Service", "flowActions": [{"type": "ui", "name": "Sample Service Form", "key": "main-form", "hasAuthRequirement": true, "runConditions": null, "schema": [{"title": "Sample Service", "description": "Configuration for Sample Service", "schema": {"defaultValues": {}, "sections": {"mainSection": {"uiType": "section", "layoutType": "grid", "children": {}}}}}]}]}