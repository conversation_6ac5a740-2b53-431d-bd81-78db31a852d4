import { HttpClient, logger } from "@sgw/common";
import { AxiosInstance } from "axios";
import { serviceConfig } from "../config";

class ApiClient {
  private apiClient: AxiosInstance;

  constructor() {
    this.apiClient = new HttpClient().client;
  }

  async submitAttachments(content: any, token: string) {
    logger.debug(`calling sequential attachment endpoint`);

    return this.apiClient.post(serviceConfig.sequential.SEQUENTIAL_SI_ATTACHMENT_URL, content, {
      headers: { "x-ccube-token": token },
    });
  }
}

export const sequentialSI = new ApiClient();
