import { logger } from "@sgw/common";
import { msfp3ApiClient } from "./apiclient";

/**
 * Supports integration with Salesforce CMS.
 */
class SalesforceService {
  /**
   * Helper method to check if the application data is to be sent to Salesforce.
   * @param applicationDestination - The destination URL to send the application to.
   * @return True if the application's destination is Salesforce, otherwise false.
   */
  public isSubmittingToSalesforce(applicationDestination?: string): boolean {
    if (!applicationDestination) return false;
    return applicationDestination.includes("salesforce.com");
  }

  /**
   * Get the application status, if any, for the user.
   * @param schemeCode - The scheme code that is to be retrieved for the user.
   * @param url - The URL for the retrieval GET request.
   */
  public async getStatus(schemeCode: string, url: string): Promise<any> {
    const apiClient = this.getApiClient(schemeCode);
    return await apiClient.getStatus(url);
  }

  /**
   * Check eligiblity criteria for pwdr.
   * @param schemeCode - The scheme code used for this check.
   * @param content - The encrypted request payload.
   */
  public async checkEligibilityCriteria(schemeCode: string, content: string): Promise<any> {
    const apiClient = this.getApiClient(schemeCode);
    const response = await apiClient.checkEligibilityCriteria(content);

    return response;
  }

  /**
   * Submits application/attachment data to destination.
   * @param schemeCode - The corresponding scheme code for this submission.
   * @param destination - The destination URL of this submission.
   * @param content - The content of the application/attachment.
   */
  public async submit(schemeCode: string, destination: string, content: string): Promise<any> {
    const apiClient = this.getApiClient(schemeCode);
    const response = await apiClient.submit(destination, content);
    logger.info(`received submit response for ${schemeCode}`, {
      destination,
      response: { status: response.status, data: response.data },
    });
    return response;
  }

  /**
   * Get the API client to use based on the scheme code.
   * @param schemeCode - Scheme code that will be used to get the corresponding API client.
   * @private
   */
  private getApiClient(schemeCode: string) {
    if (["emps", "h2w", "pwdr"].includes(schemeCode)) {
      return msfp3ApiClient;
    }

    throw new Error(`Unsupported scheme code found for Salesforce API client: ${schemeCode}`);
  }
}

export const salesforceService = new SalesforceService();
