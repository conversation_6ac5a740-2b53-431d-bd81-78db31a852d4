BASE_URL=
KOA_PORT=
KOA_KEY=

# set this to false only when testing on local without https server
HTTPS_IS_SECURE_COOKIES=
# set this to true only when testing on local with https server
HTTPS_IS_ENABLE=
HTTPS_SERVER_CERT=
HTTPS_SERVER_KEY=

CORS_ALLOW_ORIGIN=
#comma separated
CORS_ALLOW_METHODS=
#comma separated
CORS_ALLOW_HEADERS=
#in seconds
CORS_MAX_AGE=

REDIS_HOST=
REDIS_PORT=

MCF_URL=
MCF_KEY=

SSNET_BASE_URL=
SSNET_ENC_KEY=
SSNET_CLIENT_ID=
SSNET_CLIENT_SECRET=
SSNET_PROVISION_KEY=
SSNET_USER_ID=

COMLINK_ACCESS_KEY_ID=
COMLINK_ACCESS_KEY_SECRET=

AWS_S3_ASSETS_BUCKET=
AWS_S3_FILESCAN_BUCKET=
AWS_S3_ATTACHMENT_BUCKET=
AWS_S3_RETRY_BUCKET=
AWS_SQS_ATTACHMENT_PROCESSING_QUEUE=

AWS_RDS_HOST=
AWS_RDS_PORT=
AWS_RDS_DBNAME=
AWS_RDS_USER=
AWS_RDS_PASSWORD=

EMAIL_HOST=
EMAIL_PORT=
EMAIL_SECURE=
EMAIL_REJECT_UNAUTHORIZED=
EMAIL_FROM=

ENQUIRY_CMS_URL=
ENQUIRY_SERVICE_SG_EMAIL=
ENQUIRY_ZENDESK_EMAIL=
ENQUIRY_SHARED_SGW_EMAIL=

NDI_CLIENT_ID=
NDI_AUTHORIZATION_URL=
NDI_TOKEN_URL=
NDI_USERINFO_URL=
NDI_JWKS=
NDI_REDIRECT_URI=
NDI_ISSUER=
NDI_ENC_PRIVATE_KEY=
NDI_ENC_PRIVATE_KEY_ID=
NDI_SIG_PRIVATE_KEY=
NDI_SIG_PRIVATE_KEY_ID=
NDI_ALLOWED_REDIRECT_ORIGIN=

FILESCAN_QUERY_MAX_COUNT=
FILESCAN_QUERY_INTERVAL_MS=

APPGEN_ENC_PRIVATE_KEY=
APPGEN_SIG_PRIVATE_KEY=
APPGEN_SIG_KID=
DRAFT_ENC_PRIVATE_KEY=
DRAFT_ENC_JWKS=
CONSENT_ENC_PRIVATE_KEY=
CONSENT_ENC_JWKS=

MOCK_AGENCY_ENC_PRIVATE_KEY=
MOCK_AGENCY_SIG_PRIVATE_KEY=
MOCK_AGENCY_SIG_KID=

# Salesforce - MSF P3 (PWD)
MSFP3_ACCESS_TOKEN_URL=
MSFP3_ACCESS_PRIVATE_KEY=
MSFP3_ACCESS_ISSUER_ID=
MSFP3_ACCESS_SUBJECT=
MSFP3_ACCESS_AUDIENCE=
PWDR_ELIGIBILITY_URL=

# Sequential SI
SEQUENTIAL_SI_SIG_PRIVATE_KEY=
SEQUENTIAL_SI_API_KEY=
SEQUENTIAL_SI_SIG_KID=
SEQUENTIAL_SI_ATTACHMENT_URL=
SEQUENTIAL_SI_ATTACHMENT_URL_WITHOUT_GATEWAY=
