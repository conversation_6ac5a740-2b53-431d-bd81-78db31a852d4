import { loadEnvVars } from "@sgw/common";

class Config {
  private _config = {
    app: {
      NODE_ENV: process.env.NODE_ENV || "local",
      BASE_URL: "",
      SERVER_ID: "",
      DRAFT_EXPIRY_DAYS: 0,
    },
    koa: {
      KOA_PORT: "",
      K<PERSON>_KEY: "",
    },
    cors: {
      CORS_ALLOW_ORIGIN: "",
      CORS_ALLOW_METHODS: "",
      CORS_ALLOW_HEADERS: "",
      CORS_MAX_AGE: "",
    },
    aws: {
      AWS_S3_ASSETS_BUCKET: "",
      AWS_S3_FILESCAN_BUCKET: "",
      AWS_S3_ATTACHMENT_BUCKET: "",
      AWS_DYNAMODB_FILESCAN_TABLE: "",
      AWS_DYNAMODB_DRAFT_TABLE: "",
      AWS_DYNAMODB_REPORT_TABLE: "",
      A<PERSON>_SQS_ATTACHMENT_PROCESSING_QUEUE: "",
    },
    https: {
      HTTPS_IS_SECURE_COOKIES: false,
      HTTPS_IS_ENABLE: false,
      HTTPS_SERVER_CERT: "",
      HTTPS_SERVER_KEY: "",
    },
    redis: {
      REDIS_HOST: "",
      REDIS_PORT: 0,
    },
    my_careers_future: {
      MCF_URL: "",
      MCF_KEY: "",
    },
    enquiry: {
      ENQUIRY_CMS_URL: "",
      ENQUIRY_SERVICE_SG_EMAIL: "",
      ENQUIRY_ZENDESK_EMAIL: "",
      ENQUIRY_SHARED_SGW_EMAIL: "",
    },
    ndi: {
      ndi: {
        NDI_CLIENT_ID: "",
        NDI_AUTHORIZATION_URL: "",
        NDI_TOKEN_URL: "",
        NDI_USERINFO_URL: "",
        NDI_JWKS: "",
        NDI_REDIRECT_URI: "",
        NDI_ISSUER: "",
        NDI_ENC_PRIVATE_KEY: "",
        NDI_ENC_PRIVATE_KEY_ID: "",
        NDI_SIG_PRIVATE_KEY: "",
        NDI_SIG_PRIVATE_KEY_ID: "",
        NDI_ALLOWED_REDIRECT_ORIGIN: "",
      },
    },
    file: {
      FILESCAN_QUERY_MAX_COUNT: 30,
      FILESCAN_QUERY_INTERVAL_MS: 500,
    },
    mock: {
      MOCK_AGENCY_SIG_KID: "",
      MOCK_AGENCY_ENC_PRIVATE_KEY: "",
      MOCK_AGENCY_SIG_PRIVATE_KEY: "",
    },
    sequential: {
      SEQUENTIAL_SI_SIG_PRIVATE_KEY: "",
      SEQUENTIAL_SI_API_KEY: "",
      SEQUENTIAL_SI_SIG_KID: "",
      SEQUENTIAL_SI_ATTACHMENT_URL_WITHOUT_GATEWAY: "",
    },
  };

  public get app() {
    return this._config.app;
  }

  public set serverId(serverId: string) {
    this.app.SERVER_ID = serverId;
  }

  public get koa() {
    return this._config.koa;
  }

  public get cors() {
    return this._config.cors;
  }

  public get aws() {
    return this._config.aws;
  }

  public get https() {
    return this._config.https;
  }

  public get redis() {
    return this._config.redis;
  }

  public get my_careers_future() {
    return this._config.my_careers_future;
  }

  public get enquiry() {
    return this._config.enquiry;
  }

  public get ndi() {
    return this._config.ndi;
  }

  public get file() {
    return this._config.file;
  }

  public get mock() {
    return this._config.mock;
  }

  public get sequential() {
    return this._config.sequential;
  }

  public init(): void {
    for (const property in this._config) {
      loadEnvVars(this._config[property]);
    }
  }
}

export const appConfig = new Config();
