import Koa from "koa";

import { logger } from "@sgw/common";
import { schemeDao } from "@sgw/services";

import { get, save } from "./service";

export const saveDraft = async (ctx: Koa.Context): Promise<void> => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { schemeCode: omittedSchemeCode, step, ...draftContent } = ctx.request.body || {}; //used as an "omit"
  const { schemeCode } = ctx.params;

  if (["emps", "h2w", "pwdr"].includes(schemeCode)) {
    logger.error(`${schemeCode} does not support draft saving`);
    ctx.throw(400);
  }

  if (Object.keys(draftContent).length === 0) {
    logger.warn(`${schemeCode} draft invalid request`);
    ctx.throw(400);
  }

  const schema = await schemeDao.getActiveScheme(ctx.params.schemeCode);
  if (!schema) {
    logger.error(`${schemeCode} does not exist or not active`);
    ctx.throw(400);
  }

  try {
    logger.info(`${schemeCode} attempting to save draft`);
    ctx.body = await save(schemeCode, ctx.session.userId, JSON.stringify(draftContent));
  } catch (err) {
    logger.error(`${schemeCode} draft error`, err);
    ctx.throw(500);
  }
};

export const getDraft = async (ctx: Koa.Context): Promise<void> => {
  const { schemeCode } = ctx.params;

  const schema = await schemeDao.getActiveScheme(schemeCode);
  if (!schema) {
    logger.error(`${schemeCode} does not exist or not active`);
    ctx.throw(400);
  }

  try {
    logger.info(`${schemeCode} attempting to get draft`);
    ctx.body = await get(schemeCode, ctx.session.userId);
  } catch (err) {
    logger.error(`${schemeCode} draft error`, err);
    ctx.throw(500);
  }
};
