import { MyInfo } from "@govtechsg/singpass-myinfo-oidc-helper";
import DayJS from "dayjs";
import { get, set, isEmpty, omit } from "lodash";
import mergeWith from "lodash/mergeWith";
import { v5 as uuidv5 } from "uuid";
import z from "zod";
import { Attachment } from "nodemailer/lib/mailer";
import {
  addressValidator,
  application,
  ApplicationSchema,
  ApplySchema,
  countryOfBirthValidator,
  CustomFieldDynamicDropdownSchema,
  dateOfBirthValidator,
  emailValidator,
  evaluateCriteria,
  GroupMemberSubType,
  logger,
  maritalStatusValidator,
  mobileNumberValidator,
  nameValidator,
  nationalityValidator,
  nricValidator,
  raceValidator,
  residentialStatusValidator,
  SectionMemberSubType,
  sexValidator,
  isPrefillSourceMyInfoNric,
  msfBankRefundValidator,
  generateApplicationSchema,
} from "@sgw/common";
import {
  Application,
  applicationDao,
  Consent,
  consentDao,
  draftDao,
  dynamicOptionsDao,
  emailService,
  schemeDao,
  Scheme,
  sgwApiClient,
  sgwService,
  salesforceService,
  User,
  userDao,
  sqsService,
  s3Service,
  reportDao,
  sequentialSI,
} from "@sgw/services";

import { appConfig } from "../../config/app";
import { sessionService } from "../../server/session";
import { SgwSession } from "../../server/session/interfaces";
import {
  filterNullable,
  generateRefID,
  getDateForEmail,
  getHashedString,
  getUniqueValues,
  redactNric,
  redactEmail,
} from "../../util";
import { deleteDraft } from "../draft/service";
import { enquiryApiClient } from "../enquiry/apiclient";
import { myInfoService } from "../ndi/service-myinfo";
import {
  sgwApplicantConsentTemplate,
  sgwHouseholdConsentTemplate,
  sgwOutstandingDocumentsSuccessTemplate,
  sgwSuccessTemplate,
} from "./email-templates";
import { validateAndMap } from "./prefill-mapper";
import { sgwRedisService } from "./redis";
import { getApplicationState, getEligibilityFromMyinfo } from "./service-eligibility";
import {
  apiToSgwAppStatus,
  dbToApiAppStatus,
  flattenObjectToPaths,
  formatMaintenanceMessage,
  getApplicationDisplayDate,
  SGW_CATEGORY_CODE,
  SGW_STATUS_CODE,
  SGW_STATUS_VALUE,
  ConsentRequired,
  TERMINAL_STATUS,
} from "./util";
import {
  getMsfBankRefundInSchema,
  processMsfBankRefundFields,
  processMsfLetterOfUndertakingFields,
  processMsfPaynowRefundFields,
} from "./service-pdf";
import { MyInfoComponents } from "@govtechsg/singpass-myinfo-oidc-helper/dist/myinfo";
import { importPKCS8, SignJWT } from "jose";
import { randomUUID } from "node:crypto";

export const checkFiles = async (
  schema: ApplySchema,
  appSchema: ApplicationSchema,
  schemeCode: string,
  userId: string,
  step: AttachmentStep,
  submissionData,
) => {
  if (appSchema.fileUploadExternal) {
    logger.info(`${schemeCode}: file upload is to external service, skipping file existence check`);

    const fileUploadData = getAttachmentIds("fileUpload", schema, submissionData)
      .flatMap((id) => get(submissionData, id) ?? [])
      .filter((file) => file?.fileId);

    const externalFileValidation = z.array(z.object({ fileId: z.string().min(1) })).safeParse(fileUploadData); // ensure that fileId is a non-empty string
    if (!externalFileValidation.success) {
      logger.error(`${schemeCode}: file upload external validation failed`, externalFileValidation.error);
      return undefined;
    }

    return [];
  } else {
    logger.info(`${schemeCode}: checking if all files in application exists`);
    try {
      const hashedFileNames = getHashedFileNames(schema, submissionData);
      const filesExist = await checkFilesExist(`${userId}/${schemeCode}/${step}/`, hashedFileNames);

      if (!filesExist) {
        throw new Error(`${schemeCode}: some submitted file names do not exist`);
      }

      return hashedFileNames;
    } catch (error) {
      logger.error(`${schemeCode}: error checking files for ${step}`, error);
      return undefined;
    }
  }
};

export const checkSignatureFiles = async (
  schema: ApplySchema,
  schemeCode: string,
  userId: string,
  step: AttachmentStep,
  submissionData,
) => {
  const hashedSignatureFileNames = getAttachmentIds("signature", schema, submissionData)
    .map((id) => {
      const signatureFile = get(submissionData, id);
      return signatureFile ? getHashedString(`${signatureFile.attachmentType}/${signatureFile.fileName}`) : "";
    })
    .filter((hashedFileName) => hashedFileName !== "");

  if (hashedSignatureFileNames.length > 0) {
    return await checkFilesExist(`${userId}/${schemeCode}/${step}/`, hashedSignatureFileNames);
  } else {
    return true;
  }
};

const checkFilesExist = async (prefix: string, fileNames: string[]): Promise<boolean> => {
  let filesExist = true;

  try {
    const objKeys: string[] = await s3Service.listObjectKeys(appConfig.aws.AWS_S3_ATTACHMENT_BUCKET, prefix);

    for (const fileName of fileNames) {
      const objKey = `${prefix}${fileName}`;
      if (!objKeys.includes(objKey)) {
        logger.error(`${objKey} not found in attachment bucket`);
        filesExist = false;
      }
    }
  } catch (error) {
    logger.error("error verifying existence of submitted application files", error);
    filesExist = false;
  }

  return filesExist;
};

export const getHashedFileNames = (schema: ApplySchema, submissionData: any): string[] => {
  const fileUploadIds = getAttachmentIds("fileUpload", schema, submissionData);

  return fileUploadIds
    .flatMap((id) => {
      const fileUpload = get(submissionData, id);
      return fileUpload?.map((file) => getHashedString(`${file.attachmentType}/${file.fileName}`));
    })
    .filter((fileName) => fileName !== undefined);
};

export const getConsent = async (consenterUuid: string, refId: string) => {
  return await consentDao.get(consenterUuid, refId);
};

export const sendAttachmentMessage = async (
  schemeCode: string,
  refId: string,
  userId: string,
  step: string,
  hashedFileNames: string[],
  destination?: string,
) => {
  await sqsService.sendMessage(appConfig.aws.AWS_SQS_ATTACHMENT_PROCESSING_QUEUE, hashedFileNames.join(","), {
    SchemeCode: {
      DataType: "String",
      StringValue: schemeCode,
    },
    RefId: {
      DataType: "String",
      StringValue: refId,
    },
    UserId: {
      DataType: "String",
      StringValue: userId,
    },
    Step: {
      DataType: "String",
      StringValue: step,
    },
    ...(destination && {
      Destination: {
        DataType: "String",
        StringValue: destination,
      },
    }),
  });
};

export const retrieveSchema = async (schemeCode: string) => {
  logger.info(`retrieving schema for ${schemeCode}`);
  try {
    const data = await schemeDao.getSchema(schemeCode);
    if (!data) {
      logger.warn(`${schemeCode} is inactive or does not exists`);
      return;
    }

    const schema = application.parse(data.schema);
    const dynamicDropdowns: CustomFieldDynamicDropdownSchema[] = getMembers(
      schema.schema,
      "CUSTOM_FIELD",
      "DYNAMIC_DROPDOWN",
    );

    if (dynamicDropdowns.length > 0) {
      await insertOptions(dynamicDropdowns, schemeCode);
    }

    return schema;
  } catch (error) {
    logger.error(`${schemeCode} schema is invalid`, error);
    throw error;
  }
};

const insertOptions = async (dynamicDropdowns: CustomFieldDynamicDropdownSchema[], schemeCode: string) => {
  logger.info("getting dynamic dropdown options");
  const urls: string[] = [];
  dynamicDropdowns.forEach((dropdown) => urls.push(dropdown.url));

  const uniqueUrls = getUniqueValues(urls);
  const queries = uniqueUrls.map((url) => getOptions(url, schemeCode));

  const queriesResult = await Promise.allSettled(queries);
  queriesResult.forEach((res) => {
    if (res.status === "fulfilled") {
      const { url, options } = res.value;
      dynamicDropdowns.forEach((dropdown) => {
        if (dropdown.url === url) {
          dropdown.options = options;
        }
      });
    } else {
      throw new Error(res.reason);
    }
  });
  logger.info("successfully populated dynamic dropdown options");
};

const optionsValidator = z.record(z.string());
export const getOptions = async (url: string, schemeCode: string) => {
  logger.info(`getting options for ${url}`);
  let options: Record<string, string> | null = null;

  options = await getRedisOptions(url);

  if (!options) {
    options = await getSchemeApiOptions(url, schemeCode);
  }

  if (!options) {
    options = await getDbOptions(url);
  }

  return { url, options };
};

export const getRedisOptions = async (url: string) => {
  let options: Record<string, string> | null = null;
  try {
    options = await sgwRedisService.getDynamicOptions(url);
  } catch (err) {
    logger.error(`unable to get options from redis for ${url}`, err);
  }
  return options;
};

const getSchemeApiOptions = async (url: string, schemeCode: string) => {
  let options: Record<string, string> | null = null;
  logger.info(`no options from redis, getting options from agency`, url);
  try {
    const fetchedOptions = await sgwApiClient.getDynamicCodeTable(url, schemeCode);
    const scheme = await schemeDao.getActiveScheme(schemeCode);
    const decryptedOptions = await sgwService.decryptJwt(fetchedOptions, scheme);

    logger.info("options received from agency", decryptedOptions);
    if (decryptedOptions) {
      options = optionsValidator.parse(JSON.parse(decryptedOptions));
      await dynamicOptionsDao.upsert(uuidv5(url, sgwRedisService.DYNAMIC_OPTIONS_NAMESPACE), options);
      await sgwRedisService.setDynamicOptions(url, options);
    }
  } catch (err) {
    logger.error(`unable to get options from url for ${url}`, err);
  }

  return options;
};

export const getApiOptions = async (url: string) => {
  let options: Record<string, string> | null = null;
  logger.info(`no options from redis, getting options from url`, url);
  try {
    const rawOptions = await enquiryApiClient.getDynamicCodeTable(url);
    options = optionsValidator.parse(rawOptions);
    await dynamicOptionsDao.upsert(uuidv5(url, sgwRedisService.DYNAMIC_OPTIONS_NAMESPACE), options);
    await sgwRedisService.setDynamicOptions(url, options);
  } catch (err) {
    logger.error(`unable to get options from url for ${url}`, err);
  }

  return options;
};

// storing options in db may result in option order change due to storing as jsonb
export const getDbOptions = async (url: string) => {
  let options: Record<string, string> | null = null;
  logger.info(`no options from agency, getting options from db`);
  try {
    options = (await dynamicOptionsDao.get(uuidv5(url, sgwRedisService.DYNAMIC_OPTIONS_NAMESPACE))) || null;
    if (options) {
      await sgwRedisService.setDynamicOptions(url, options);
    } else {
      logger.warn(`no options found in db`);
      throw new Error(`unable to get options from db`);
    }
  } catch (err) {
    logger.error(`unable to get options from db for ${url}`);
    throw err;
  }

  return options;
};

export const updateSubmittedSchemeCount = async (grant: string): Promise<void> => {
  logger.info(`updating submitted ${grant} count`);
  try {
    await reportDao.incrementSchemeCount(grant);
  } catch (error) {
    logger.error(`error updating ${grant} grant count`, error);
  }
};

export const getAppFromDb = async (userId: string, grant: string): Promise<Application[]> => {
  logger.info("retrieving applications from database");
  try {
    const applications = await applicationDao.getApplications(userId, grant);

    if (applications.length !== 0) {
      logger.info("database applications retrieved", applications);
      return applications;
    }
  } catch (error) {
    logger.error(`error getting ${grant} application from database`, error);
  }
  return [];
};

export const getAppByRefIdFromDb = async (userId: string, refId: string): Promise<Application | undefined> => {
  logger.info("retrieving application from database");
  try {
    const application = await applicationDao.getApplicationByRefId(userId, refId);

    if (application) {
      logger.info("database application retrieved", application);
      return application;
    }
  } catch (error) {
    logger.error(`error getting ${refId} application from database`, error);
  }

  return undefined;
};

interface UserData {
  nric: string;
  userId: string;
}
interface ConsenterDetails {
  name: string;
  nric: string;
}

interface ApplicationData {
  refId: string;
  submissionData: any;
  requestBody: any;
  hashedFileNames: string[];
  consenterDetails: ConsenterDetails[];
  emailAttachments: Attachment[];
}

export interface SchemeContent {
  refId: string;
  nextSteps: string;
  name: string;
}

export type AttachmentStep = "application" | "outstanding-documents";

export interface SchemeDataMap {
  [schemeCode: string]: {
    appSchema: ApplicationSchema;
    applySchema: ApplySchema;
    submissionData: any;
    hashedFileNames: string[];
  };
}

export const extractSchemeDataFromBundledSubmission = async (
  schemeCodes: string[],
  schemaId: string,
  requestBody: any,
  nric: string,
): Promise<SchemeDataMap> => {
  const schemeDataMapPromises = schemeCodes.map(async (schemeCode) => {
    const appSchema = await retrieveSchema(schemeCode);
    const applySchema = appSchema?.schema.find((schema) => schema.id === schemaId);

    if (!appSchema || !applySchema) {
      throw new Error(`${schemeCode}: no active schema found for individual scheme, id - ${schemaId}`);
    }

    const validator = generateApplicationSchema(applySchema);
    const validate = validator.safeParse(requestBody);
    if (!validate.success) {
      throw new Error(`${schemeCode}: individual scheme validation failed`);
    }
    const updatedSubmissionData = setMyInfoNric(applySchema, validate.data, nric);
    const hashedFileNames = appSchema.fileUploadExternal ? [] : getHashedFileNames(applySchema, updatedSubmissionData);

    return {
      schemeCode,
      data: {
        appSchema,
        applySchema,
        submissionData: updatedSubmissionData,
        hashedFileNames,
      },
    };
  });

  const schemeDataMap = await Promise.all(schemeDataMapPromises);
  return schemeDataMap.reduce((acc, { schemeCode, data }) => {
    acc[schemeCode] = data;
    return acc;
  }, {} as SchemeDataMap);
};

export const checkInvalidBankBranch = (schema: ApplySchema, requestBody: any): boolean => {
  const msfBankRefundIds = getMsfBankRefundInSchema(schema, requestBody);
  const msfBankValidator = msfBankRefundValidator();

  return msfBankRefundIds.some(({ fullId }) => {
    const value = get(requestBody, fullId);

    // value might not be present due to conditional, treated as valid
    if (!value) {
      return false;
    }

    const validation = msfBankValidator.safeParse(value);
    if (!validation.success && validation.error.issues[0]?.path[0] === "bankBranch") {
      logger.error("bank branch validation error likely due to mapping issue", value);
      return true;
    }

    return false;
  });
};

export const duplicateAttachmentsForBundledSubmission = async (
  userId: string,
  schemeCode: string,
  requestBody: any,
  schemeDataMap: SchemeDataMap,
  step: AttachmentStep,
): Promise<void> => {
  const bucketName = appConfig.aws.AWS_S3_ATTACHMENT_BUCKET;
  const sourcePrefix = `${userId}/${schemeCode}/${step}/`;

  for (const [schemeCode, { applySchema, hashedFileNames }] of Object.entries(schemeDataMap)) {
    const destinationPrefix = `${userId}/${schemeCode}/${step}/`;

    // Get signature attachment IDs and hash their file names
    const hashedSignatureFileNames = getAttachmentIds("signature", applySchema, requestBody)
      .map((id) => {
        const signatureFile = get(requestBody, id);
        return signatureFile ? getHashedString(`${signatureFile.attachmentType}/${signatureFile.fileName}`) : "";
      })
      .filter((hashedFileName) => hashedFileName !== "") as string[];

    // Combine hashed file names and copy to destination
    const keysToCopy = [...hashedFileNames, ...hashedSignatureFileNames];
    await s3Service.copyObjectsByKeys(bucketName, sourcePrefix, destinationPrefix, keysToCopy);
  }
};

export const postProcessBundledSubmission = async (
  schemeCode: string,
  userId: string,
  step: AttachmentStep,
): Promise<void> => {
  try {
    const pathPrefix = `${userId}/${schemeCode}/${step}`;
    const objKeysInPrefix = await s3Service.listObjectKeys(appConfig.aws.AWS_S3_ATTACHMENT_BUCKET, pathPrefix);

    logger.info(`${schemeCode}: starting post-processing, userId: ${userId}`);

    // Run all tasks in parallel
    await Promise.all([
      deleteDraft(schemeCode, userId),
      updateSubmittedSchemeCount(schemeCode),
      objKeysInPrefix.length > 0
        ? s3Service.deleteObjects(appConfig.aws.AWS_S3_ATTACHMENT_BUCKET, objKeysInPrefix)
        : Promise.resolve(),
    ]);
    logger.info(`${schemeCode}: post processing completed`);
  } catch (err) {
    logger.error(`${schemeCode}: post processing failed, userId: ${userId}`, err);
  }
};

export const processSubmissions = async (
  schemeDataMap: SchemeDataMap,
  requestBody: any,
  step: AttachmentStep,
  { nric, userId }: UserData,
): Promise<{ email: string; schemes: SchemeContent[] }> => {
  let email = "";
  const schemes: SchemeContent[] = [];

  for (const [schemeCode, { appSchema, applySchema, submissionData, hashedFileNames }] of Object.entries(
    schemeDataMap,
  )) {
    const { email: applicantEmail, scheme } = await processSubmission(
      requestBody,
      submissionData,
      schemeCode,
      appSchema,
      applySchema,
      hashedFileNames,
      step,
      { nric, userId },
    );
    email = applicantEmail;
    schemes.push(scheme);
  }

  return { email, schemes };
};

export const processSubmission = async (
  requestBody: any,
  updatedSubmissionData: any,
  schemeCode: string,
  appSchema: ApplicationSchema,
  applySchema: ApplySchema,
  hashedFileNames: string[],
  step: AttachmentStep,
  { nric, userId }: UserData,
): Promise<{ email: string; scheme: SchemeContent }> => {
  try {
    const userAttachmentFolder = `${userId}/${schemeCode}/${step}`;
    const emailAttachments: Attachment[] = [];

    // Process MSF refund fields
    const msfGeneratedHashedFileNames = await processMsfBankRefundFields(
      applySchema,
      requestBody,
      updatedSubmissionData,
      nric,
      userAttachmentFolder,
    );
    hashedFileNames.push(...msfGeneratedHashedFileNames);

    const msfPaynowGeneratedHashedFileNames = await processMsfPaynowRefundFields(
      applySchema,
      requestBody,
      updatedSubmissionData,
      nric,
      userAttachmentFolder,
    );
    hashedFileNames.push(...msfPaynowGeneratedHashedFileNames);

    // Process MSF Letter of Undertaking fields
    const { attachments: louAttachments, generatedHashedFileNames: louGeneratedHashedFileNames } =
      await processMsfLetterOfUndertakingFields(schemeCode, applySchema, requestBody, nric, userAttachmentFolder);
    hashedFileNames.push(...louGeneratedHashedFileNames);
    emailAttachments.push(...louAttachments);

    // Get consenter details
    const consenterDetails = getConsenterDetails(applySchema, updatedSubmissionData);

    // Generate reference ID
    const refId = generateRefID(schemeCode.toUpperCase());
    logger.info(`generated ref ID for submission ${refId}`);

    // Process SGW application
    const email = await processSgwApplication(
      appSchema,
      applySchema,
      { nric, userId },
      {
        refId,
        submissionData: updatedSubmissionData,
        requestBody,
        hashedFileNames,
        consenterDetails,
        emailAttachments,
      },
    );

    // Return result
    return {
      email: email ? redactEmail(email) : "",
      scheme: {
        refId,
        nextSteps: appSchema.nextSteps,
        name: appSchema.schemeName,
      },
    };
  } catch (error) {
    logger.error(`${schemeCode}: error processing application`, error);
    throw new Error(`${schemeCode}: error processing application`);
  }
};

export const processSgwApplication = async (
  schema: ApplicationSchema,
  applySchema: ApplySchema,
  { nric, userId }: UserData,
  { refId, submissionData, requestBody, hashedFileNames, consenterDetails, emailAttachments }: ApplicationData,
) => {
  const schemeCode = schema.schemeCode;

  const submittedDate = new Date();
  const scheme = await schemeDao.getScheme(schemeCode);
  if (!scheme) {
    throw new Error(`no scheme found at db for ${schemeCode}`);
  }

  try {
    logger.info("uploading application");
    await sgwService.uploadApplication(refId, nric, submittedDate, submissionData, scheme);
    await handleAttachmentProcessing(
      schemeCode,
      refId,
      schema,
      applySchema,
      userId,
      hashedFileNames,
      scheme.appSubmissionUrl,
      requestBody,
    );

    const application: Application = {
      refId,
      userUuid: userId,
      schemeCode,
      appliedDateTime: submittedDate,
      updatedDateTime: submittedDate,
      statusCode: "0",
    };

    let consents: Consent[] = [];
    if (consenterDetails.length > 0) {
      consents = await Promise.all(
        consenterDetails.map(async ({ nric, name }) => {
          const encryptedContent = await sgwService.createConsentJwe(JSON.stringify({ nric, name }));
          const consenterDetails = JSON.stringify({ data: encryptedContent });

          return {
            consenterUuid: sessionService.getSgwUserId(nric),
            refId,
            userUuid: userId,
            consenterDetails,
          };
        }),
      );
    }

    logger.info("saving application record to database", application);

    const { name, email } = getEmailDataFromSubmission(applySchema, submissionData);
    const user: User = { uuid: userId, name, email };
    await applicationDao.saveSgwApplication(application, user, consents);
    await deleteDraft(schemeCode, userId);
    await updateSubmittedSchemeCount(schemeCode);

    await sendSuccessEmail(
      refId,
      schema,
      applySchema,
      submissionData,
      consents.length > 0,
      undefined,
      emailAttachments,
    );
    return email;
  } catch (error) {
    logger.error(`${schemeCode}: error processing application`, error);
    throw new Error(`${schemeCode}: error processing application`);
  }
};

export const processSgwConsent = async (
  nric: string,
  consenterUuid: string,
  applicantUuid: string,
  refId: string,
  appSchema: ApplicationSchema,
  submissionData,
) => {
  const schemeCode = appSchema.schemeCode;
  const scheme = await schemeDao.getScheme(schemeCode);
  if (!scheme) {
    throw new Error(`no scheme found at db for ${schemeCode}`);
  }

  try {
    logger.info("uploading consent");
    await sgwService.uploadApplication(refId, nric, new Date(), submissionData, scheme, "consent");
    await consentDao.update(consenterUuid, refId, applicantUuid);

    const email = await sendSuccessEmail(
      refId,
      appSchema,
      appSchema.consentSchema!,
      submissionData,
      false,
      applicantUuid,
    );
    return email;
  } catch (error) {
    logger.error(`error processing ${schemeCode} consent`, error);
    throw new Error(`error processing ${schemeCode} consent`);
  }
};

const getEmailDataFromSubmission = (schema: ApplySchema, submissionData: any) => {
  const emailMembers = getMemberMatchBy(
    schema,
    (member) => member.type === "PRESET_FIELD" && member.subType === "email" && !!member.template,
  );

  // Retrieve the first non-empty email
  let email = "";
  let emailTemplate = "";
  for (const { fullId, member } of emailMembers) {
    const currentEmail = get(submissionData, fullId);
    if (currentEmail) {
      email = currentEmail;
      emailTemplate = member?.["template"];
      break;
    }
  }

  // assume applicant name is always the first name preset field in the first section
  const nameId = getMemberMatchBy(schema, (member) => member.type === "PRESET_FIELD" && member.subType === "name")[0]
    ?.fullId;
  const name = get(submissionData, nameId);

  return { email, emailTemplate, name };
};

const sendSuccessEmail = async (
  refId: string,
  appSchema: ApplicationSchema,
  applySchema: ApplySchema,
  submissionData: any,
  actionRequired = false,
  applicantUuidForConsent = "",
  emailAttachments?: Attachment[],
) => {
  const { email, emailTemplate, name } = getEmailDataFromSubmission(applySchema, submissionData);

  if (emailTemplate === "application") {
    if (email) {
      logger.info(`sending success email to ${email}`);
      await sendApplicationEmail(appSchema, name, email, refId, actionRequired, emailAttachments);
    }
  } else if (emailTemplate === "consent") {
    const applicant = await userDao.getByRefIdAndUserUuid(refId, applicantUuidForConsent);
    if (email) {
      logger.info(`sending consenter submission email to ${email}`);
      await sendHouseholdConsentEmail(name, applicant.name, refId, email, appSchema);
    }
    if (applicant?.email) {
      logger.info(`sending applicant consent email to ${applicant.email}`);
      await sendApplicantConsentEmail(name, applicant.name, refId, applicant.email, appSchema);
    }
  }

  return email;
};

const sendApplicationEmail = async (
  appSchema: ApplicationSchema,
  recipientName: string,
  recipientEmail: string,
  refId: string,
  actionRequired = false,
  attachments?: Attachment[],
  cc?: string[],
) => {
  const subject = `Application for ${appSchema.schemeName} (Ref ID: ${refId})`;
  const emailBody = sgwSuccessTemplate(recipientName, refId, getDateForEmail(), appSchema, actionRequired);
  await emailService.sendEmail([recipientEmail], subject, emailBody, cc, attachments);
};

const sendHouseholdConsentEmail = async (
  hhMemberName: string,
  applicantName: string,
  refId: string,
  to: string,
  schemeDetails: ApplicationSchema,
) => {
  const subject = `Consent for ${schemeDetails.schemeName} (Ref ID: ${refId})`;
  const htmlBody = sgwHouseholdConsentTemplate(hhMemberName, applicantName, refId, getDateForEmail(), schemeDetails);

  await emailService.sendEmail([to], subject, htmlBody);
};

const sendApplicantConsentEmail = async (
  hhMemberName: string,
  applicantName: string,
  refId: string,
  to: string,
  schemeDetails: ApplicationSchema,
) => {
  const subject = `Consent for ${schemeDetails.schemeName} (Ref ID: ${refId})`;
  const htmlBody = sgwApplicantConsentTemplate(applicantName, hhMemberName, refId, getDateForEmail(), schemeDetails);

  await emailService.sendEmail([to], subject, htmlBody);
};

const sgwApplicationSchema = z
  .object({
    refId: z.string(),
    status: z.enum(SGW_STATUS_CODE),
    appliedDateTime: z.date(),
    updatedDateTime: z.date(),
    remarks: z.string().optional(),
    detail: z
      .object({
        title: z.string(),
        category: z.enum(SGW_CATEGORY_CODE),
        assistance: z.string().max(8).optional(),
        status: z.enum(SGW_STATUS_CODE).optional(),
        description: z.string().optional(),
        period: z.string().optional(),
        paymentMode: z.string().optional(),
        disbursement: z.string().optional(),
        remarks: z.string().optional(),
      })
      .array()
      .optional(),
    outstandingItems: z
      .object({
        id: z.string(),
        deadline: z.string(),
      })
      .optional(),
    appliedForNames: z.string().array().optional(),
  })
  .refine((val) => (val.status === "20" ? !!val.outstandingItems : true), {
    message: "Pending Documents status requires outstanding items to be defined.",
  });

const agencyStatusResponse = z.object({
  application: sgwApplicationSchema.array(),
  eligible: z.boolean().optional(),
  userData: z.record(z.any()).optional(),
  systemData: z
    .object({ maintenanceStartDate: z.date().optional(), maintenanceEndDate: z.date().optional() })
    .optional(),
  ineligibleReason: z.string().optional(),
});

export type SgwApplication = z.infer<typeof sgwApplicationSchema>;
type AgencyStatusResponse = z.infer<typeof agencyStatusResponse>;

export const getApplicantDetailsForPendingConsent = async (
  consenterUuid: string,
  refId: string,
): Promise<User | undefined> => {
  logger.info(`attempting to retrieve applicant details for pending consent with refId: ${refId}`);

  const pendingConsent = await consentDao.getPendingSgwConsent(consenterUuid, refId);
  if (pendingConsent && !pendingConsent.isProvided) {
    const applicant = await userDao.getByRefIdAndUserUuid(refId, pendingConsent.userUuid);
    if (applicant) {
      logger.info("retrieved applicant details for pending consent");
      return applicant;
    }
  }

  return undefined;
};

const extractValidResponseData = (
  formattedAgencyStatus: any,
  schemeCode: string,
): { validAgencyData: AgencyStatusResponse; hasInvalidData: boolean } => {
  const parsedData = agencyStatusResponse.safeParse(formattedAgencyStatus);

  if (parsedData.success) {
    return { validAgencyData: parsedData.data, hasInvalidData: false };
  }
  const validationError = parsedData.error;
  logger.error(`${schemeCode}: agency status data failed validation ${validationError}`);

  /**
   * Top-level response data segment keys for those that have failed validation.
   * To be used for data extract of the valid segments out.
   * Sample: Set(2) { 'application', 'userData'}
   */
  const invalidDataSegmentKeys = new Set(validationError.issues.map((issue) => String(issue.path[0])));

  // rebuild the applications array if needed
  let validApplications: SgwApplication[];
  const allApplications = Array.isArray(formattedAgencyStatus.application) ? formattedAgencyStatus.application : [];

  /**
   * Applications are independent of one another.
   * If one or more application records failed validation, we only drop those specific entries
   * (logging each dropped record by its refId) and preserve the rest, so that valid applications
   * continue to be processed and applicants aren’t blocked by a single bad record.
   */
  if (invalidDataSegmentKeys.has("application")) {
    const invalidAppIndexes = new Set<number>();
    validationError.errors.forEach((e) => {
      if (e.path[0] === "application" && typeof e.path[1] === "number") {
        invalidAppIndexes.add(e.path[1] as number);
      }
    });

    if (invalidAppIndexes.size) {
      const dropped = Array.from(invalidAppIndexes).map((index) => ({
        index,
        refId: allApplications[index]?.refId,
      }));
      logger.info("Dropping invalid application records", { dropped });
    }

    validApplications = allApplications.filter((_, i) => !invalidAppIndexes.has(i));
  } else {
    validApplications = allApplications;
  }

  // prune any other invalid top-level fields
  const otherFields = omit(formattedAgencyStatus, ["application"]);
  const prunedFields = Object.fromEntries(
    Object.entries(otherFields).filter(([key, val]) => !invalidDataSegmentKeys.has(key) && val !== undefined),
  ) as Omit<AgencyStatusResponse, "application">;

  const cleaned = {
    application: validApplications,
    ...prunedFields,
  };

  return { validAgencyData: agencyStatusResponse.parse(cleaned), hasInvalidData: true };
};

interface ChildInfo {
  name: string;
  nric: string;
}

interface TemplateData {
  children?: ChildInfo[];
}

type TemplateId =
  | "notEligiblePwd"
  | "notEligiblePwdCitizen"
  | "childrenNotEligible"
  | "verifiedPwd"
  | "notVerifiedPwd"
  | "verifiedPwdChildren"
  | "noVerifiedPwdChildren";

interface ApplyTemplate {
  template: TemplateId;
  data?: TemplateData;
}

export type ApplyInfo = Record<string, ApplyTemplate>;

export const getAgencyStatus = async (
  sgwSession: SgwSession,
  scheme: Scheme,
  schemeCode: string,
): Promise<{
  appStatus: SgwApplication[];
  eligible?: boolean;
  userData?: object;
  maintenanceMsg?: string;
  ineligibleCode?: string;
  agencyUnavailable: boolean;
}> => {
  let appStatus: SgwApplication[] = [];
  let eligible: boolean | undefined;
  let userData: object | undefined;
  let maintenanceMsg: string | undefined;
  let ineligibleCode: string | undefined;
  let agencyUnavailable = false;

  try {
    logger.info("retrieving application from agency");
    const query = await sgwService.createJwt(JSON.stringify({ nric: sgwSession.nric }), scheme);

    let statusResponse;
    if (salesforceService.isSubmittingToSalesforce(scheme.appStatusUrl)) {
      statusResponse = await salesforceService.getStatus(schemeCode, `${scheme.appStatusUrl}?query=${query}`);
    } else {
      statusResponse = await sgwApiClient.getStatus(`${scheme.appStatusUrl}?query=${query}`, schemeCode);
    }

    const agencyStatus = await sgwService.decryptJwt(statusResponse, scheme);
    logger.info("application status retrieved from agency", agencyStatus);

    if (agencyStatus) {
      const formatDateFields = (key: string, value: any) =>
        ["appliedDateTime", "updatedDateTime", "maintenanceStartDate", "maintenanceEndDate"].includes(key)
          ? new Date(value)
          : value;
      const formattedAgencyStatus = JSON.parse(agencyStatus, formatDateFields);
      const { validAgencyData, hasInvalidData } = extractValidResponseData(formattedAgencyStatus, schemeCode);

      agencyUnavailable = hasInvalidData;
      appStatus = validAgencyData.application.sort(byApplicationDateDesc);
      eligible = validAgencyData.eligible;
      userData = validAgencyData.userData;
      ineligibleCode = validAgencyData.ineligibleReason;
      if (validAgencyData.systemData?.maintenanceStartDate) {
        maintenanceMsg = formatMaintenanceMessage(
          validAgencyData.systemData.maintenanceStartDate,
          validAgencyData.systemData.maintenanceEndDate,
        );
      }
    }

    return { appStatus, eligible, userData, maintenanceMsg, ineligibleCode, agencyUnavailable };
  } catch (error) {
    logger.error(`unable to retrieve application status from agency with scheme code: ${schemeCode}`, error);
    throw new Error(`Agency service unavailable`);
  }
};

type ResidentialStatusCode = Required<MyInfoComponents.Schemas.PersonBasic>["residentialstatus"]["code"];
type ChildrenBirthRecords = MyInfoComponents.Schemas.PersonBasic["childrenbirthrecords"];

export const checkForeignerEligibility = (resStatusCode: ResidentialStatusCode) => {
  return resStatusCode === "A";
};

export const checkChildrenEligibility = async (
  childrenBirthRecords: ChildrenBirthRecords = [],
  requesterNric: string,
  scheme: Scheme,
) => {
  const ineligibleChildren = (
    await Promise.all(
      childrenBirthRecords.map(async (child) => {
        const subjectNric = child.birthcertno?.value;
        if (!subjectNric) return null;

        const eligible = await checkPwdEligibility(subjectNric, requesterNric, scheme);

        return !eligible ? { name: child.name?.value ?? "", nric: subjectNric } : null;
      }),
    )
  ).filter(filterNullable);

  return ineligibleChildren;
};

/**
 * Performs computation logic to retrieve relevant schemes' application info.
 * Currently supports PWDR/DVF, EMPS & H2W schemes, where application info is
 * determined through additional eligibility checks.
 * @param schemeCode
 * @param sgwSession
 * @returns
 */
export const getApplyInfo = async (schemeCode: string, sgwSession: SgwSession): Promise<ApplyInfo> => {
  if (!["pwdr", "emps", "h2w"].includes(schemeCode)) return {};

  // for pwdr, emps & h2w the apply info is determined by foreigner checks and pwdr registered status.
  const scheme = await schemeDao.getActiveScheme(schemeCode);
  if (!scheme) {
    throw new Error(`${schemeCode} scheme does not exist or not active`);
  }

  const applyInfo: ApplyInfo = {};
  const { nric: requesterNric, myInfoCommon } = sgwSession;
  const resStatus = myInfoCommon?.residentialstatus?.code;
  const childrenBirthRecords = myInfoCommon?.childrenbirthrecords;

  // Shared check #1: Foreigner status
  const isForeigner = checkForeignerEligibility(resStatus);

  // Shared check #2: Applicant eligibility (using "pwdr" for all)
  let isApplicantEligible: boolean | null = null;
  if (!isForeigner) {
    isApplicantEligible = await checkPwdEligibility(requesterNric, requesterNric, scheme);
  }
  // Shared check #3: Children eligibility (using "pwdr" for all)
  const ineligibleChildren = await checkChildrenEligibility(childrenBirthRecords, requesterNric, scheme);

  // Evaluate eligibility check results
  // Determine the message for the main applicant (`applyInfo.pwd`)
  if (isForeigner) {
    applyInfo.pwd = { template: "notEligiblePwdCitizen" };
  } else if (schemeCode === "pwdr" && !isApplicantEligible) {
    // applying for pwdr, and applicant is ineligible
    applyInfo.pwd = { template: "notEligiblePwd" };
  } else if (schemeCode !== "pwdr") {
    // applying for emps/h2w
    applyInfo.pwd = isApplicantEligible ? { template: "notVerifiedPwd" } : { template: "verifiedPwd" };
  }

  // Determine the message for the children (`applyInfo.parent`)
  if (ineligibleChildren.length) {
    applyInfo.parent = {
      template: schemeCode === "pwdr" ? "childrenNotEligible" : "verifiedPwdChildren",
      data: { children: ineligibleChildren },
    };
  } else if (schemeCode !== "pwdr") {
    // applying for emps and there are no ineligible children for emps/h2w
    applyInfo.parent = {
      template: "noVerifiedPwdChildren",
    };
  }

  return applyInfo;
};

const checkPwdEligibility = async (subjectNric: string, requesterNric: string, scheme: Scheme): Promise<boolean> => {
  let pwdEligible = true;
  try {
    logger.info(
      `${scheme.code}: checking eligibility for nric: ${redactNric(subjectNric)}, requester nric: ${redactNric(
        requesterNric,
      )}`,
    );

    const payload = { requester: requesterNric, schemeCode: scheme.code, criteria: { nric: subjectNric } };
    const jwt = await sgwService.createJwt(JSON.stringify(payload), scheme);
    const raw = await salesforceService.checkEligibilityCriteria(scheme.code, JSON.stringify({ data: jwt }));
    const decrypted = await sgwService.decryptJwt(raw, scheme);

    if (decrypted) {
      const { eligible, checkedDateTime } = JSON.parse(decrypted);
      logger.info(
        `${scheme.code}: eligiblity check completed for nric: ${redactNric(
          subjectNric,
        )} at ${checkedDateTime}, requester nric: ${redactNric(requesterNric)}`,
      );
      if (eligible === false) {
        pwdEligible = false;
      }
    }
  } catch (error) {
    logger.warn(
      `${scheme.code}: eligibility check failed for nric: ${redactNric(subjectNric)}, requester nric: ${redactNric(
        requesterNric,
      )}`,
    );
  }
  return pwdEligible;
};

export const getAppStatusResponse = async (schemeCode: string, sgwSession: SgwSession) => {
  let agencyUnavailable: boolean;
  let agencyAppStatus: SgwApplication[] = [];
  let agencyEligible: boolean | undefined;
  let userData: object | undefined;
  let maintenanceMsg: string | undefined;
  let ineligibleCode: string | undefined;
  let applyInfo: ApplyInfo | undefined;

  const applicationSchema = await retrieveSchema(schemeCode);
  if (!applicationSchema) {
    throw new Error(`no active schema found for ${schemeCode}`);
  }

  const scheme = await schemeDao.getActiveScheme(schemeCode);
  if (!scheme) {
    throw new Error(`scheme ${schemeCode} does not exists`);
  }

  let dbAppStatus = await getAppFromDb(sgwSession.userId, schemeCode);

  try {
    ({
      appStatus: agencyAppStatus,
      eligible: agencyEligible,
      userData,
      maintenanceMsg,
      ineligibleCode,
      agencyUnavailable,
    } = await getAgencyStatus(sgwSession, scheme, schemeCode));
  } catch (error) {
    agencyUnavailable = true;
  }

  if (agencyAppStatus.length > 0) {
    dbAppStatus = await updateApplicationDatabase(schemeCode, sgwSession.userId, agencyAppStatus, dbAppStatus);
  }
  const appStatus = constructApplicationList(agencyAppStatus, dbAppStatus);

  let consentsRequired: ConsentRequired[] = [];
  if (appStatus.length > 0) {
    consentsRequired = await getConsentsRequired(appStatus);
  }

  const hasDraft = await draftDao.isDraftExist(sgwSession.userId, schemeCode);
  const latestApplicationStatus =
    appStatus.length > 0 ? (apiToSgwAppStatus(schemeCode, appStatus[0]).status as SGW_STATUS_VALUE) : undefined;

  const myinfoEligible = getEligibilityFromMyinfo(sgwSession, applicationSchema.myinfoEligibilityCriteria);
  let applicationState = getApplicationState(
    agencyEligible,
    myinfoEligible,
    latestApplicationStatus,
    applicationSchema.agencyControlled,
    hasDraft,
    agencyUnavailable,
  );

  // only applicable for new applications that do not have a draft
  // user data is used to prefill for new application form
  if (applicationState === "allow" && !hasDraft) {
    try {
      sgwSession.userData = {
        ...sgwSession.userData,
        ...validateAndMap(schemeCode, userData),
      };
      await sessionService.updateSession(sgwSession);
    } catch (error) {
      applicationState = "maintenance";
      logger.error(
        `prefill data from agency failed validation and unable to save mapping to session for ${schemeCode}`,
        error,
      );
    }
  }

  return {
    schemeCode,
    schemeName: applicationSchema.schemeName,
    status: appStatus,
    agencyUnavailable,
    applicationState,
    consentsRequired,
    maintenanceMsg,
    ineligibleCode,
    applyInfo,
  };
};

export const updateApplication = async (
  latestAppStatus: SgwApplication,
  userUuid: string,
  schemeCode: string,
): Promise<void> => {
  logger.info(`updating application status to database`, latestAppStatus);
  // detail, remarks and appliedForNames are not stored in db
  const { detail, remarks, appliedForNames, status, outstandingItems, ...application } = latestAppStatus;
  const latestApplication: Application = {
    ...application,
    statusCode: status,
    userUuid,
    schemeCode,
    documentsType: outstandingItems?.id,
    documentsDeadline: outstandingItems?.deadline ? getApplicationDisplayDate(outstandingItems.deadline) : undefined,
  };
  try {
    await applicationDao.upsert(latestApplication);
  } catch (error) {
    logger.error(`error updating ${schemeCode} application status to database`, error);
  }
  logger.info(" updating application status to database complete");
};

export const deleteApplication = async (userUuid: string, refId: string): Promise<void> => {
  logger.info(`deleting application ${refId} from database`);
  try {
    await applicationDao.delete(userUuid, refId);
  } catch (error) {
    logger.error(`failed to delete application ${refId} from database`, error);
  }
};

interface ApplicationWithAppliedDateTime {
  appliedDateTime?: Date | string;
}
export function byApplicationDateDesc(a: ApplicationWithAppliedDateTime, b: ApplicationWithAppliedDateTime) {
  return DayJS(b.appliedDateTime).valueOf() - DayJS(a.appliedDateTime).valueOf();
}

export function byApplicationDateAsc(a: ApplicationWithAppliedDateTime, b: ApplicationWithAppliedDateTime) {
  return DayJS(a.appliedDateTime).valueOf() - DayJS(b.appliedDateTime).valueOf();
}

function isAppliedDateNewer(a: SgwApplication | Application, b: SgwApplication | Application) {
  return a.refId !== b.refId && DayJS(a.appliedDateTime).isAfter(DayJS(b.appliedDateTime));
}

function isLatestUpdatedApplication(a: SgwApplication | Application, b: SgwApplication | Application) {
  return a.refId === b.refId && DayJS(a.updatedDateTime).isAfter(DayJS(b.updatedDateTime));
}

export const isConsentRequired = async (statusCode: string, refId: string): Promise<boolean> => {
  // consent is only required on Submitted, Received and Processing status
  if (["0", "1", "2"].includes(statusCode)) {
    const consents = await consentDao.getByRefId(refId);
    return consents.length > 0;
  }

  return false;
};

const getConsentsRequired = async (appStatus: SgwApplication[]) => {
  const consentPromises = appStatus.map(async (application) => {
    try {
      const isRequired = await isConsentRequired(application.status, application.refId);
      return { refId: application.refId, isRequired };
    } catch (error) {
      logger.warn(`failed to check if consent is needed for ${application.refId}`, error);
      return { refId: application.refId, isRequired: false };
    }
  });

  return await Promise.all(consentPromises);
};

export const getMembers = (schema: Record<string, any> | any[], type: string, subType: string): any[] => {
  const result: any[] = [];

  if (!Array.isArray(schema) && schema.type === type && schema.subType === subType) {
    result.push(schema);
  }

  Object.values(schema).forEach((member) => {
    if (member && typeof member === "object") {
      result.push(...getMembers(member, type, subType));
    }
  });

  return result;
};

// Generates a list of full IDs for multi-value field based on the length of the multi-value array.
export const getMultiValueFieldFullIds = (multiValueIds: string[], submissionData) => {
  return multiValueIds.flatMap((id) => {
    const [namespace, field] = id.split("[index].");
    const multiValue = get(submissionData, namespace);

    return (Array.isArray(multiValue) && multiValue.map((_, index) => `${namespace}.${index}.${field}`)) || [];
  });
};

const getFileUploadIds = (schema: ApplySchema) =>
  getMemberMatchBy(
    schema,
    (member) => (member.type === "CUSTOM_GROUP" || member.type === "CUSTOM_FIELD") && member.subType === "FILE_UPLOAD",
  ).map(({ fullId }) => fullId);

const getSignatureIds = (schema: ApplySchema) => {
  const signatureCustomFieldIds = getMemberMatchBy(
    schema,
    (member) => member.type === "CUSTOM_FIELD" && member.subType === "SIGNATURE",
  ).map(({ fullId }) => fullId);

  const signatureIdsInMsfBankRefund = getMemberMatchBy(
    schema,
    (member) => member.type === "PRESET_FIELD" && member.subType === "msfBankRefund",
  ).flatMap(({ fullId }) => {
    return [`${fullId}.recipientSignature`, `${fullId}.jointAllAccount.jointAllSignature`];
  });

  const signatureIdsInPresetField = getMemberMatchBy(
    schema,
    (member) =>
      member.type === "PRESET_FIELD" &&
      (member.subType === "msfLetterOfUndertaking" ||
        member.subType === "msfOmnibusConsent" ||
        member.subType === "msfPaynowRefund"),
  ).map(({ fullId }) => fullId);

  return [...signatureCustomFieldIds, ...signatureIdsInMsfBankRefund, ...signatureIdsInPresetField];
};

export const getAttachmentIds = (attachmentType: "fileUpload" | "signature", schema: ApplySchema, submissionData) => {
  const attachmentIds = attachmentType === "fileUpload" ? getFileUploadIds(schema) : getSignatureIds(schema);

  const uniqueAttachmentIds = getUniqueValues(attachmentIds);
  const multiValueIds = uniqueAttachmentIds.filter((id) => id.includes("[index]."));

  if (multiValueIds.length === 0) {
    return uniqueAttachmentIds;
  }

  const multiValueFieldFullIds = getMultiValueFieldFullIds(multiValueIds, submissionData);
  const filteredAttachmentIds = uniqueAttachmentIds.filter((id) => !id.includes("[index]."));

  return [...filteredAttachmentIds, ...multiValueFieldFullIds];
};

const myInfoValidator = z.object({
  name: nameValidator(),
  nric: nricValidator(),
  sex: sexValidator(),
  race: raceValidator(),
  nationality: nationalityValidator(),
  dob: dateOfBirthValidator(),
  countryOfBirth: countryOfBirthValidator(),
  address: addressValidator(),
  email: emailValidator(true),
  mobileNumber: mobileNumberValidator(true),
  residentialStatus: residentialStatusValidator(),
  maritalStatus: maritalStatusValidator(),
  child: z
    .object({
      childLifeStatus: z.enum(["A", "D"]).optional(),
      childName: nameValidator(),
      childIdNumber: nricValidator(),
      childDob: dateOfBirthValidator(),
      childSex: sexValidator(),
    })
    .array()
    .optional(),
});

export const getMyInfoIds = (schemas: ApplicationSchema["schema"]) => {
  const clearableMyInfoIds: string[] = [];
  const myInfoOptionsIds: string[] = [];

  schemas.map((schema) => {
    getMemberWithSource(schema, "myInfo").map(({ fullId, member }) => {
      if (!member["editable"] && !isPrefillSourceMyInfoNric(member["prefillSource"])) {
        clearableMyInfoIds.push(fullId);
      }
    });

    getMemberMatchBy(schema, (member) => {
      return member.type === "PRESET_FIELD" && (member.subType === "child" || member.subType === "family");
    }).map(({ fullId }) => myInfoOptionsIds.push(fullId));
  });

  return { clearableMyInfoIds, myInfoOptionsIds };
};

export const getSchemeMyInfo = (schema: ApplySchema, myInfoRaw: MyInfo.MyInfoComponents.Schemas.PersonBasic) => {
  const mappedMyInfo = myInfoService.mapper(myInfoRaw);
  const myInfoValidateResult = myInfoValidator.safeParse(mappedMyInfo);
  if (!myInfoValidateResult.success) {
    logger.error("myInfo data failed validation", myInfoValidateResult.error);
    throw new Error("myInfo data failed validation");
  }

  const prefillData = {};
  Object.entries(mappedMyInfo)
    .filter(([key]) => key !== "child" && key !== "nric")
    .forEach(([key, value]) => {
      getMemberWithSource(schema, `myInfo.${key}`).forEach(({ fullId }) => {
        prefillData[fullId] = value;
      });
    });

  let childOptions;
  const childMembers = getMemberMatchBy(
    schema,
    (member) => member.type === "PRESET_FIELD" && (member.subType === "child" || member.subType === "family"),
  );
  if (childMembers.length > 0 && mappedMyInfo.child && mappedMyInfo.child.length > 0) {
    childOptions = {};
    mappedMyInfo.child.forEach((child) => {
      const { childLifeStatus, ...childDetails } = child;
      if (childLifeStatus === "A" && childDetails.childIdNumber) {
        const childHashId = uuidv5(childDetails.childIdNumber, myInfoService.MYINFO_UUID_NAMESPACE);
        childOptions[childHashId] = childDetails;
      }
    });
  }

  return { data: prefillData, ...(childOptions && { options: { child: childOptions } }) };
};

export interface Member {
  fullId: string;
  member: GroupMemberSubType | SectionMemberSubType;
}

const getMemberWithSource = (schema: ApplySchema, prefillSource: string) => {
  return getMemberMatchBy(schema, (member) => {
    if ("prefillSource" in member && member.prefillSource) {
      return member.prefillSource.startsWith(prefillSource);
    }
    return false;
  });
};

export const getMemberMatchBy = (
  schema: ApplySchema,
  isMatch: (memberSchema: GroupMemberSubType | SectionMemberSubType) => boolean,
): Member[] => {
  const members: Member[] = [];
  for (const section of schema.section) {
    members.push(...getNestedMemberMatchBy(section.id, section.member, isMatch));
  }
  return members;
};

const getNestedMemberMatchBy = (
  namespace: string,
  members: (GroupMemberSubType | SectionMemberSubType)[],
  isMatch: (memberSchema: GroupMemberSubType | SectionMemberSubType) => boolean,
): Member[] => {
  const schemas: Member[] = [];

  for (const schema of members) {
    switch (schema.type) {
      case "SECTION_CONDITIONAL":
      case "GROUP_CONDITIONAL":
        // match the entire conditional itself
        if (isMatch(schema)) {
          schemas.push({ fullId: `${namespace}.${schema.id}}`, member: schema });
        }
        // match its selector
        if (isMatch(schema.selector)) {
          schemas.push({ fullId: `${namespace}.${schema.id}.${schema.selector.id}`, member: schema.selector });
        }
        // match any member for all conditional results
        schema.result.forEach((result) => {
          schemas.push(...getNestedMemberMatchBy(`${namespace}.${schema.id}`, result.member, isMatch));
        });
        break;
      case "CUSTOM_GROUP":
        if (isMatch(schema)) {
          schemas.push({ fullId: `${namespace}.${schema.id}`, member: schema });
        }
        if (schema.subType === "BLANK") {
          schemas.push(...getNestedMemberMatchBy(`${namespace}.${schema.id}`, schema.member, isMatch));
        }
        break;
      case "PRESET_FIELD":
      case "CUSTOM_FIELD":
        if (isMatch(schema)) {
          schemas.push({ fullId: `${namespace}.${schema.id}`, member: schema });
        }
        break;
      case "MULTI_VALUE":
        // match the entire multi value itself
        if (isMatch(schema)) {
          schemas.push({ fullId: `${namespace}.${schema.id}`, member: schema });
        }
        // match group in multi value
        schemas.push(
          ...getNestedMemberMatchBy(
            `${namespace}.${schema.id}.${schema.group.id}[index]`,
            schema.group.member,
            isMatch,
          ),
        );
        break;
      case "DECORATOR":
        if (isMatch(schema)) {
          schemas.push({ fullId: "", member: schema });
        }
        break;
    }
  }
  return schemas;
};

export const getOutstandingItemsSchema = (
  schema: NonNullable<ApplicationSchema["outstandingDocumentsSchema"]>,
  outstandingItems: string,
): ApplicationSchema["outstandingDocumentsSchema"] => {
  const outstandingItemsList = outstandingItems.split(",");

  const outstandingMembers = schema.section[0].member.filter((member) => {
    // backend does not care about decorators during submission, exclude them from schema
    if (member.type === "DECORATOR") {
      return false;
    }

    // if comment box is specified in schema, ignore outstanding items specified by agency and always include
    if (member.type === "CUSTOM_FIELD" && member.subType === "MULTILINE_TEXT") {
      return true;
    }

    return outstandingItemsList.includes(member.id);
  });

  if (outstandingMembers.length > 0) {
    schema.section[0].member = outstandingMembers;
    return schema;
  }

  logger.warn("no outstanding items found");
  return undefined;
};

export const sendOutstandingDocumentsSuccessEmail = async (
  userId: string,
  refId: string,
  schemeDetails: ApplicationSchema,
) => {
  const user = await userDao.getUser(userId);
  if (user && user.email && user.name) {
    const subject = `Documents Submission for ${schemeDetails.schemeName} (Ref ID: ${refId})`;
    const htmlBody = sgwOutstandingDocumentsSuccessTemplate(user.name, refId, getDateForEmail(), schemeDetails);

    await emailService.sendEmail([user.email], subject, htmlBody);
    return user.email;
  }

  return undefined;
};

export const processOutstandingDocuments = async (
  session: SgwSession,
  scheme: Scheme,
  applicationSchema: ApplicationSchema,
  documentsSchema: ApplySchema,
  appStatus: Application,
  submissionData,
  hashedFileNames: string[],
) => {
  let email: string | undefined;

  const nonFileUploadFields = getMemberMatchBy(
    documentsSchema,
    (member) =>
      !((member.type === "CUSTOM_GROUP" || member.type === "CUSTOM_FIELD") && member.subType === "FILE_UPLOAD"),
  );
  const shouldSubmitApplication = nonFileUploadFields.length > 0;

  try {
    if (shouldSubmitApplication) {
      // Application data is submitted only if there are input fields excluding file uploads.
      const submittedDate = new Date();

      logger.info(`uploading application for outstanding documents of ${scheme.code}`);
      await sgwService.uploadApplication(
        appStatus.refId,
        session.nric,
        submittedDate,
        submissionData,
        scheme,
        "outstandingItems",
      );
    }

    logger.info(`total files submitted for outstanding documents ${hashedFileNames.length}`);
    if (hashedFileNames.length > 0) {
      await sendAttachmentMessage(
        scheme.code,
        appStatus.refId,
        session.userId,
        "outstanding-documents",
        hashedFileNames,
        scheme.appSubmissionUrl,
      );
    }

    if (appStatus.statusCode === "20") {
      // TODO: Multiple applications: Update the saving of applications by refId when the database is ready for multiple application entries.
      // update db application to Documents Submitted status from Pending Documents
      const latestApplication: Application = { ...appStatus, statusCode: "21", updatedDateTime: new Date() };
      logger.info("updating application record to database", latestApplication);
      await applicationDao.upsert(latestApplication);
    }

    email = await sendOutstandingDocumentsSuccessEmail(session.userId, appStatus.refId, applicationSchema);
  } catch (error) {
    logger.error("error processing outstanding documents", error);
    throw new Error("error processing outstanding documents");
  }

  return email;
};

/**
 * Return prefill data in an object with its key denoting full id of the field and value denoting the value to be prefilled for the field
 * e.g.
 * ```
 * {
 *  "profile.name": "Tan Ke Xuan"
 * }
 * ```
 * @param schema used to determine which field needed prefilling i.e. field with `prefillSource` specified that is not "myInfo.XX"
 * @param userData value used for prefilling
 * @returns
 */
export const getPrefillData = (schema: ApplySchema, userData: SgwSession["userData"]) => {
  const members = getMemberMatchBy(schema, (member) =>
    "prefillSource" in member && member.prefillSource && !member.prefillSource.startsWith("myInfo") ? true : false,
  );

  if (members.length === 0) {
    return undefined;
  }

  if (!userData) {
    logger.error("no user data found for prefilling");
    return undefined;
  }

  let result = {};

  members.forEach(({ fullId, member }) => {
    const prefillValue = get(userData, member["prefillSource"]);
    if (!prefillValue) {
      return;
    }

    if (member.type !== "MULTI_VALUE") {
      result[fullId] = prefillValue;
    } else if (member.type === "MULTI_VALUE" && Array.isArray(prefillValue)) {
      // Get prefill value for multi-value's dropdown
      result[`${fullId}.${member.group.id}Count`] = prefillValue.length.toString();

      const memberToPrefill: { id: string; prefillSource: string }[] = [];

      // Recursive function to get members that need prefilling
      const getPrefillMembers = (groupMembers, parentId = "") => {
        const members: { id: string; prefillSource: string }[] = [];

        groupMembers.forEach((groupMember) => {
          // Check if the group member has a prefill source and add it
          if ("prefillSource" in groupMember && groupMember.prefillSource) {
            members.push({
              id: `${parentId}${groupMember.id}`,
              prefillSource: groupMember.prefillSource,
            });
          } else if (groupMember.type === "GROUP_CONDITIONAL") {
            // If the conditional group has a selector with a prefill source, add it
            if (groupMember.selector.prefillSource) {
              members.push({
                id: `${parentId}${groupMember.id}.${groupMember.selector.id}`,
                prefillSource: groupMember.selector.prefillSource,
              });
            }

            // Recursively get members from the GROUP_CONDITIONAL result
            groupMember.result.forEach((result) => {
              members.push(...getPrefillMembers(result.member, `${parentId}${groupMember.id}.`));
            });
          }
        });

        return members;
      };

      memberToPrefill.push(...getPrefillMembers(member.group.member));

      // Prefill values
      prefillValue.forEach((prefillItem, index) => {
        const groupNamespace = `${fullId}.${member.group.id}.${index}`;
        memberToPrefill.forEach(({ id, prefillSource }) => {
          const prefillVal = prefillItem[prefillSource];
          if (!isEmpty(prefillVal)) {
            if (Array.isArray(prefillVal) || typeof prefillVal !== "object") {
              // Directly assign arrays or simple values
              result[`${groupNamespace}.${id}`] = prefillVal;
            } else {
              // Flatten and assign object values
              result = {
                ...result,
                ...flattenObjectToPaths(prefillVal, `${groupNamespace}.${id}.`),
              };
            }
          }
        });
      });
    }
  });

  return result;
};

// populate full NRIC if the field is retrieved from MyInfo data source
export const setMyInfoNric = (schema: ApplySchema, data: any, nric: string) => {
  const myInfoNrics = getMemberMatchBy(
    schema,
    (member) =>
      member.type === "PRESET_FIELD" && member.subType === "nric" && isPrefillSourceMyInfoNric(member.prefillSource),
  );

  myInfoNrics.forEach((member) => {
    set(data, member.fullId, nric);
  });

  return data;
};

export const getConsenterDetails = (schema: ApplySchema, data: any): ConsenterDetails[] => {
  const consenterDetails: { name: string; nric: string }[] = [];

  // Retrieve a list of members who need to give consent.
  const consentInfos = getMemberMatchBy(schema, (member) => "consent" in member).map(({ fullId, member }) => {
    const consentCondition =
      "consent" in member && typeof member.consent === "object" && !isEmpty(member.consent) ? member.consent : null;

    let fullNricId = fullId;
    let fullNameId: string | undefined;

    if (member.type === "PRESET_FIELD" && member.subType === "family") {
      // Since family preset field are in a nested schema and the structure is hardcoded in the frontend,
      // We need to specify the NRIC and Name ID field explicitly for retrieval.
      fullNricId = `${fullId}.memberIdNumber`;
      fullNameId = `${fullId}.memberName`;
    } else if ("consenterInfo" in member && member.consenterInfo?.name) {
      const namespace = fullId.split(".").slice(0, -1).join(".");
      fullNameId = `${namespace}.${member.consenterInfo.name}`;
    }

    if (!fullNameId) {
      throw new Error("unable to proceed without consenter full name id");
    }

    return { fullNricId, fullNameId, consentCondition };
  });

  logger.info(`gathered consenter objects with NRIC and name ids in schema. Total ${consentInfos.length}`, {
    consentInfos,
  });

  // Process each consenter info to retrieve their details based on the provided data.
  consentInfos.forEach(({ fullNricId, fullNameId, consentCondition }) => {
    const itemsToProcess = retrieveItems(fullNricId, fullNameId, data);

    // Process each item, checking consent conditions and extracting NRIC and Name values.
    itemsToProcess.forEach(({ dataItem, nricId, nameId }) => {
      if (evaluateConsentCondition(dataItem, consentCondition)) {
        const nric = get(dataItem, nricId);
        const name = get(dataItem, nameId);

        if (nric && !name) {
          throw new Error("unable to proceed as consenter need to have both NRIC and name");
        }

        if (nric && name) {
          consenterDetails.push({ nric: nric.toUpperCase(), name: name.toUpperCase() });
        }
      }
    });
  });

  return consenterDetails;
};

const retrieveItems = (fullNricId: string, fullNameId: string, data: any) => {
  if (fullNricId.includes("[index].")) {
    // Handle multi-value fields and create a list of individual items within that field.
    const [namespace, nricId] = fullNricId.split("[index].");
    const [, nameId] = fullNameId.split("[index].");

    const arrayData = get(data, namespace);

    return Array.isArray(arrayData) && arrayData.length > 0
      ? arrayData.map((dataItem) => ({ dataItem, nricId, nameId }))
      : [];
  }

  // Extract nricId, nameId, and parent data from the same level in the data structure.
  const nricParts = fullNricId.split(".");
  const nricId = nricParts.slice(-1)[0];

  const nameParts = fullNameId.split(".");
  const nameId = nameParts.slice(-1)[0];

  const namespace = nricParts.slice(0, -1).join(".");
  const fieldParentData = get(data, namespace);

  return [{ dataItem: fieldParentData, nricId, nameId }];
};

export const evaluateConsentCondition = (dataItem, consentCondition): boolean => {
  if (!consentCondition) return true;

  const { field, operator, value } = consentCondition;
  return evaluateCriteria({ type: "value", sourcePath: field, operator, value }, dataItem);
};

export const getDuplicateNricSections = (schema: ApplySchema, data: any): string[] => {
  // retrieve a list of nric IDs based on member conditions:
  // 1. nric of members who needs to give consent
  // 2. nric of child family members
  // 3. nric of the main applicant (prefilled from MyInfo)
  const nricIds = getUniqueValues(
    getMemberMatchBy(
      schema,
      (member) =>
        member.type === "PRESET_FIELD" &&
        (member.subType === "child" ||
          (member.type === "PRESET_FIELD" && member.subType === "consenter") ||
          member.subType === "family" ||
          (member.subType === "nric" && !member.skipDuplicateCheck)),
    ).map(({ fullId, member }) => {
      // since child and family preset fields are in a nested schema and the structure is hardcoded in the frontend,
      // we need to specify the nric ID field explicitly for retrieval.
      if (member.type === "PRESET_FIELD" && ["child", "family"].includes(member.subType)) {
        const suffixId = member.subType === "child" ? "childIdNumber" : "memberIdNumber";
        return `${fullId}.${suffixId}`;
      }

      if (member.type === "PRESET_FIELD" && member.subType === "consenter") {
        return `${fullId}.nric`;
      }

      return fullId;
    }),
  );

  logger.info(`gathered nric ids in schema for duplication check. Total ${nricIds.length}`, { nricIds });

  const nrics = nricIds
    .flatMap((nricId) => {
      if (nricId.includes("[index].")) {
        const [namespace, field] = nricId.split("[index].");
        const multiValue = get(data, namespace);
        return (multiValue || []).map((value) => get(value, field)?.toUpperCase());
      }

      const nric = get(data, nricId);
      return nric?.toUpperCase() || [];
    })
    .filter(Boolean);

  if (nrics.length === new Set(nrics).size) return [];

  const sectionIds = new Set(nricIds.map((nricId) => nricId.split(".")[0]));
  return schema.section.filter(({ id }) => sectionIds.has(id)).map(({ title }) => title);
};

export const getConsenterOptions = async (refId: string): Promise<Record<string, string> | undefined> => {
  const results = await consentDao.getConsenterDetails(refId);

  if (results.length === 0) return undefined;

  try {
    // Retrieve the array of objects containing NRIC and Name after decryption.
    const options = (
      await Promise.all(
        results.map(async (consent) => {
          if (!consent.consenterDetails?.data) {
            // application submitted before the consenter feature does not have consenter details stored in our system
            logger.warn("consenter details is empty");
            return null;
          }

          const decryptedConsenterDetails = await sgwService.decryptConsentJwe(consent.consenterDetails.data);
          if (!decryptedConsenterDetails) {
            throw new Error("unsuccesful decryption of consenter details");
          }
          const { nric, name } = JSON.parse(decryptedConsenterDetails);

          return { nric, name };
        }),
      )
    ).filter(filterNullable);

    if (options.length === 0) {
      return undefined;
    }

    // Sort the options by name, then construct an object with NRIC as the key and Name as the value.
    const sortedOptions = options
      .sort((a, b) => a.name.localeCompare(b.name))
      .reduce((acc, { nric, name }) => {
        acc[nric] = name;
        return acc;
      }, {});

    return sortedOptions;
  } catch (error) {
    logger.error(`error retrieving consenter details for ${refId}`, error);
    return undefined;
  }
};

/**
 * Constructs a list of application statuses by comparing agency statuses with database statuses.
 */
export function constructApplicationList(agencyAppStatus: SgwApplication[], dbAppStatus: Application[]) {
  const finalList: SgwApplication[] = [];

  // Create a map for quick reference to dbStatus by refId
  const dbStatusMap = new Map<string, Application>(dbAppStatus.map((dbEntry) => [dbEntry.refId, dbEntry]));

  for (const agencyStatus of agencyAppStatus) {
    const dbMatch = dbStatusMap.get(agencyStatus.refId);

    // Add the agency status directly to the final list if no match
    if (!dbMatch) {
      finalList.push(agencyStatus);

      continue;
    }

    // Determine whether to use the database entry or the agency status
    if (isLatestUpdatedApplication(dbMatch, agencyStatus)) {
      logger.info(`using database record ${dbMatch.refId}, as it is more recent than agency`, dbMatch);
      finalList.push(dbToApiAppStatus(dbMatch));
    } else {
      finalList.push(agencyStatus);
    }

    // Remove matched entry from the map to avoid duplicates later
    dbStatusMap.delete(dbMatch.refId);
  }

  // Add remaining unmatched database statuses to the final list
  if (dbStatusMap.size > 0) {
    // Convert Map values into an array because logger doesn't support logging Map objects directly
    const unmatchedDbEntries = Array.from(dbStatusMap.values());
    logger.info(
      `adding ${unmatchedDbEntries.length} unmatched database records to application list`,
      unmatchedDbEntries,
    );

    unmatchedDbEntries.forEach((entry) => {
      finalList.push(dbToApiAppStatus(entry));
    });
  }

  return finalList.sort(byApplicationDateDesc);
}

/**
 *  Get the latest reference ID from agency and database applications
 */
const getLatestRefId = (agencyAppStatus: SgwApplication[], dbAppStatus: Application[]) => {
  let latestRefId = "";

  if (agencyAppStatus.length > 0 && dbAppStatus.length > 0) {
    const latestAgency = agencyAppStatus[0];
    const latestDb = dbAppStatus[0];

    // Determine if the agency application is newer or more recent than database application
    if (isAppliedDateNewer(latestAgency, latestDb) || isLatestUpdatedApplication(latestAgency, latestDb)) {
      latestRefId = latestAgency.refId;
    } else {
      latestRefId = latestDb.refId;
    }
  } else if (agencyAppStatus.length > 0) {
    latestRefId = agencyAppStatus[0].refId;
  } else if (dbAppStatus.length > 0) {
    latestRefId = dbAppStatus[0].refId;
  }

  return latestRefId;
};

const getStatusValue = (schemeCode: string, application: SgwApplication) => {
  return apiToSgwAppStatus(schemeCode, application).status as SGW_STATUS_VALUE;
};

/**
 * Updates the database with the latest agency statuses.
 * - Compares agency entries with database entries to determine which are latest.
 * - Updates ongoing statuses, deletes terminal entries, and creates new records as needed.
 * - Cleans up any outdated terminal entries from the database.
 */
export async function updateApplicationDatabase(
  schemeCode: string,
  userId: string,
  agencyAppStatus: SgwApplication[],
  dbAppStatus: Application[],
) {
  let haveUpdates = false;

  const latestRefId = getLatestRefId(agencyAppStatus, dbAppStatus);

  // Create a Map for quick lookup of database entries by refId
  const dbEntryMap = new Map(dbAppStatus.map((entry) => [entry.refId, entry]));

  // Iterate over each agency application for updates or new entries to the database
  for (const agencyEntry of agencyAppStatus) {
    const dbEntry = dbEntryMap.get(agencyEntry.refId);

    // Create a new record if no corresponding database entry exists
    if (!dbEntry) {
      const status = getStatusValue(schemeCode, agencyEntry);

      if (agencyEntry.refId === latestRefId || !TERMINAL_STATUS.includes(status)) {
        await updateApplication(agencyEntry, userId, schemeCode);
        haveUpdates = true;
      }

      continue;
    }

    // Determine if the existing database entry needs to be updated
    const shouldUpdate = isLatestUpdatedApplication(agencyEntry, dbEntry);

    if (shouldUpdate) {
      const status = getStatusValue(schemeCode, agencyEntry);

      if (agencyEntry.refId === latestRefId || !TERMINAL_STATUS.includes(status)) {
        await updateApplication(agencyEntry, userId, schemeCode);
      } else {
        // Delete record if it's a non-latest entry that has transitioned to a terminal status
        await deleteApplication(userId, agencyEntry.refId);
      }
      haveUpdates = true;

      // Remove the processed refId from the Map to prevent further processing
      dbEntryMap.delete(agencyEntry.refId);
    }
  }

  // Clean up any remaining entries in dbEntryMap that have not been processed
  // eg. Entries that exist only in the database or those that match an agency application that is later or the same
  for (const [refId, dbEntry] of dbEntryMap) {
    const status = getStatusValue(schemeCode, dbToApiAppStatus(dbEntry));

    // Delete non-latest entry that is in terminal status
    if (refId !== latestRefId && TERMINAL_STATUS.includes(status)) {
      await deleteApplication(userId, dbEntry.refId);
      haveUpdates = true;
    }
  }

  return haveUpdates ? await getAppFromDb(userId, schemeCode) : dbAppStatus;
}

const replaceArrayCustomizer = (_, srcValue) => {
  if (Array.isArray(srcValue)) {
    return srcValue;
  }

  return undefined;
};

export const mergeUserData = (schemes: string[], userData: SgwSession["userData"]) => {
  if (!userData) {
    return {};
  }

  const result = schemes.reduce((mergedData, scheme) => {
    const matchingKey = Object.keys(userData).find((key) => key === scheme);

    if (matchingKey && userData[matchingKey]) {
      return mergeWith(mergedData, userData[matchingKey], replaceArrayCustomizer);
    }

    return mergedData;
  }, {});

  return result;
};

export const hasScfaBeneficiary = (schemeCode: string, schema: ApplySchema, data: any): boolean => {
  const fieldId = "isBeneficiary"; //TODO keep track of ids with special logic handling in a file
  const beneficiaryId = getMemberMatchBy(schema, (member) => member.type === "CUSTOM_FIELD" && member.id === fieldId)[0]
    ?.fullId;

  if (!beneficiaryId || !beneficiaryId.includes("[index].")) {
    throw new Error(`${schemeCode}: ${fieldId} not found in schema or not multi-valued`);
  }

  const [namespace, field] = beneficiaryId.split("[index].");
  const multiValue = get(data, namespace);
  const beneficiaryValues = Array.isArray(multiValue)
    ? multiValue.map((value) => get(value, field)).filter(Boolean)
    : [];

  return beneficiaryValues.some((value) => value === "0");
};

export const handleAttachmentProcessing = async (
  schemeCode: string,
  refId: string,
  schema: ApplicationSchema,
  applySchema: ApplySchema,
  userId: string,
  hashedFileNames: string[],
  appSubmissionUrl: string,
  requestBody: any,
) => {
  if (schema.fileUploadExternal) {
    // Case 1: External processing.
    await sendExternalAttachmentSubmission(schemeCode, refId, applySchema, requestBody);
  } else if (hashedFileNames.length > 0) {
    // Case 2: Internal processing, runs only when files were uploaded to our S3
    logger.info("sending attachment hashed filenames to queue");
    await sendAttachmentMessage(schemeCode, refId, userId, "application", hashedFileNames, appSubmissionUrl);
  }
};

export const sendExternalAttachmentSubmission = async (
  schemeCode: string,
  refId: string,
  applySchema: ApplySchema,
  requestBody: any,
): Promise<void> => {
  const attachments = getAttachmentIds("fileUpload", applySchema, requestBody).flatMap((id) =>
    (get(requestBody, id) ?? [])
      .filter((file) => file?.fileId)
      .map((file) => ({
        fileId: file?.fileId,
        fileName: file?.fileName,
        id: file?.attachmentType,
      })),
  );

  if (attachments.length === 0) {
    logger.info(`${schemeCode}: no external attachment found`);
    return;
  } 

  const payload = {
    refId,
    schemeCode,
    attachments,
  };

  logger.info(`${schemeCode}: triggering external attachment submission`, payload);

  try {
    const hashedPayload = getHashedString(JSON.stringify(payload));
    const token = await genJwtToken(appConfig.sequential.SEQUENTIAL_SI_ATTACHMENT_URL_WITHOUT_GATEWAY, hashedPayload);

    await sequentialSI.submitAttachments(payload, token);

    logger.info(`${schemeCode}: successfully triggered external attachment submission`);
  } catch (error) {
    logger.error(`${schemeCode}: failed to trigger external attachment submission`, error);
  }
};

export const genJwtToken = async (destinationUrl: string, shaPayloadData?: string): Promise<string> => {
  const privateKey = await importPKCS8(appConfig.sequential.SEQUENTIAL_SI_SIG_PRIVATE_KEY, "ES256");
  const payload = shaPayloadData ? { data: shaPayloadData } : {};

  try {
    // Build and sign the JWT
    const token = new SignJWT(payload)
      .setProtectedHeader({ typ: "JWT", alg: "ES256", kid: appConfig.sequential.SEQUENTIAL_SI_SIG_KID })
      .setIssuedAt()
      .setExpirationTime("180s")
      .setAudience(destinationUrl)
      .setSubject("POST")
      .setJti(randomUUID())
      .setIssuer(appConfig.sequential.SEQUENTIAL_SI_API_KEY)
      .sign(privateKey);
    return token;
  } catch (error) {
    logger.error(`Error generating JWT token for ${destinationUrl}`, error);
    throw new Error("Failed to generate JWT token");
  }
};
