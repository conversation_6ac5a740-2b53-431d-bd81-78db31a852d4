# P0001: Sequential JSON Schema Generator

- [Summary](#summary)
- [Motivation](#motivation)
  - [Goals](#goals)
  - [Non-Goals](#non-goals)
- [Proposal](#proposal)
  - [Acceptance criteria](#acceptance-criteria)
    - [AC 1: TypeScript-based Schema Definition](#ac-1-typescript-based-schema-definition)
    - [AC 2: Automated JSON Generation](#ac-2-automated-json-generation)
    - [AC 3: Modular Flow Action Management](#ac-3-modular-flow-action-management)
  - [Notes/Constraints/Caveats](#notesconstraintscaveats)
  - [Risks and Mitigation](#risks-and-mitigation)
- [Design Details](#design-details)
- [Alternatives](#alternatives)
- [Infrastructure Needed](#infrastructure-needed)

## Summary

This proposal introduces a TypeScript-based schema generation system for
Sequential JSON configurations. Instead of manually creating and maintaining
large JSON files (potentially thousands of lines), developers will define
schemas using TypeScript interfaces and generate JSON configurations
programmatically. This approach reduces duplication, minimizes errors, and
improves maintainability for Sequential form flow configurations.

## Motivation

Currently, Sequential service configurations are created manually as JSON files.
This manual approach presents several challenges:

1. **Error-prone**: Large JSON files are difficult to validate and debug
2. **Duplication**: Similar flow actions require copy-pasting large blocks
3. **Collaboration difficulties**: Multiple developers cannot easily work on
   the same configuration simultaneously
4. **Maintenance overhead**: Updates to common patterns require changes across
   multiple locations
5. **Scalability concerns**: As we onboard more form flows, manual creation
   becomes unsustainable

### Goals

- Eliminate manual JSON schema creation for Sequential configurations
- Reduce code duplication through reusable TypeScript components
- Enable collaborative development through modular flow action files
- Provide type safety for Sequential schema generation
- Support multiple environments and versioning strategies

### Non-Goals

- Modifying the Sequential platform itself
- Creating a visual schema builder interface
- Replacing existing manually-created schemas immediately (migration will be
  gradual)
- Supporting non-Sequential form flow platforms

## Proposal

Create a new TypeScript-based schema generation system by developing a new
`@sgw/sequential` package that allows developers to define Sequential
configurations programmatically and generate JSON files automatically.

### Acceptance criteria

#### AC 1: TypeScript-based Schema Definition

- Developers can define service configurations using TypeScript interfaces
- Environment-specific configuration support (dev/staging/prod)
- Dedicated enum folder for storing field options, especially for select fields with large number of options
- Shared/common components that can be reused across different flow actions

#### AC 2: Automated JSON Generation

- Each form flow has its own command to generate JSON files from TypeScript definitions
- Output organized by environment and version (`/dev/v1.0.0/service-name.json`)

#### AC 3: Modular Flow Action Management

- Each flow action defined in separate TypeScript file
- Flow actions can be developed and tested independently
- Easy composition of flow actions into complete service configurations
- Version control friendly (smaller, focused files)

### Notes/Constraints/Caveats

- Generated schemas must maintain 100% compatibility with Sequential platform
- TypeScript interfaces must evolve alongside Sequential platform updates

### Risks and Mitigation

**Risk**: Generated schemas incompatible with Sequential platform
**Mitigation**: Comprehensive testing against known working schemas

**Risk**: Breaking changes in Sequential platform affecting generated schemas
**Mitigation**: Version TypeScript interfaces and maintain backward
compatibility layers

## Design Details

We will create a new `@sgw/sequential` package to house the TypeScript-based
schema generation system.

### File Structure

```
sgw/packages/sequential/
├── utils/                    # Utility functions shared across forms
│   ├── generators/           # JSON generation logic
│   │   └── generate-json.ts  # Main generation script
│   ├── types/                # TypeScript interfaces
│   │   ├── index.ts     # Core Sequential types
│   └── builders/             # Schema builder utilities
│       ├── service-config.ts # Service config builder
│       ├── flow-action.ts    # Flow action builder
│       └── ui-components.ts  # UI component builders
└── schemes/                  # Form flow definitions
    ├── ihl/                  # IHL service example
    │   ├── service.ts        # Service definition
    │   ├── service-config.ts # Service config schema
    │   ├── flow-actions/     # Individual flow actions
    │   │   ├── dashboard.ts
    │   │   └── profile.ts
    │   └── enums/            # Field options and enums
    │       ├── residential-status.ts
    │       └── education-level.ts
    └── other-form/           # Other form flows
        ├── service.ts
        ├── service-config.ts
        ├── flow-actions/
        └── enums/
```

### Core TypeScript Interfaces

```typescript
interface SequentialServiceConfig {
  actionConfig: ActionConfig;
  copyConfig: CopyConfig;
  behaviourConfig: BehaviourConfig;
  sessionDuration: number;
  sessionDataLifetime: number;
  flowActions: FlowAction[];
}

interface FlowAction {
  type: FlowActionType.UI;
  name: string;
  key: string;
  hasAuthRequirement: boolean;
  runConditions: any;
  schema: UISchema[];
}
```

### Generation Workflow

1. Developer defines service configuration in TypeScript
2. Individual flow actions created as separate TypeScript modules
3. Build script composes flow actions into complete service configuration
4. JSON files generated for specified environments and versions

## Alternatives

**Alternative 1**: Continue manual JSON creation

- Pros: No development overhead, immediate use
- Cons: Scalability issues, error-prone, difficult collaboration

## Infrastructure Needed

- Extension of existing `@sgw/sequential` package
- Documentation hosting for developer guides and examples
- Validation service for generated schemas (optional)
