import 'dotenv/config'
import 'reflect-metadata'
import 'es6-shim'

import { ClassSerializerInterceptor } from '@nestjs/common'
import { NestFactory, Reflector } from '@nestjs/core'
import { NestExpressApplication } from '@nestjs/platform-express'
import { LoggerService } from '@sgw/commons'
import { useContainer } from 'class-validator'
import cookieParser from 'cookie-parser'

import { AppModule } from './nestjs/app.module'
import { ConfigService } from './nestjs/core/config/config.service'
import { nest } from './nestjs/nest'

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bufferLogs: true
  })
  const config: ConfigService = app.get(ConfigService)

  app.use(cookieParser())
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)))
  app.set('trust proxy', 1)

  app.useLogger(app.get(LoggerService))
  nest.logConfig(app)
  nest.useStaticAssets(app)
  nest.useClassValidationPipe(app)
  useContainer(app.select(AppModule), { fallbackOnErrors: true })
  nest.useSwagger(app, config.swagger)
  nest.useGlobalFilters(app, config)

  await app.listen(config.app.port)
  console.info(`App listening on ${config.app.port}`)
}
bootstrap()
