import { Prisma } from '@sgw/database'

import { GroupBaseDto } from './group.base.dto'

// For GroupsService #findAndCountRelationship

export class GroupIncludeCountServiceDto
  extends GroupBaseDto
  implements
    Prisma.GroupGetPayload<{
      include: { _count: true }
    }>
{
  _count: Prisma.GroupCountOutputType & { Agency: number } // Added  "{ Agency: number }" due to Prisma.SchemeCountOutputType not generating correctly for optional fields
}
