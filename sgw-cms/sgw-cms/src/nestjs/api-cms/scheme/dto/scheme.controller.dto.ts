import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { ContentType, Language, Prisma, SchemeTranslation } from '@sgw/database'
import { Transform, Type } from 'class-transformer'
import {
  IsArray,
  IsBoolean,
  isEmail,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsPositive,
  IsString,
  IsUrl,
  isURL,
  Length,
  MaxLength,
  ValidateIf,
  ValidateNested
} from 'class-validator'

import { LONG_TEXT_MAX_LENGTH } from '../../../../commons/const/data'
import { ContactType } from '../../../../commons/const/enum'
import { CustomValidateIf } from '../../../../commons/validations'
import {
  GroupsOnSchemesIncludeGroupDto,
  SchemeBaseDto,
  SchemeBaseIncludesDto
} from './scheme.base.dto'

// SchemeController#CustomVIew
export class SchemeCustomViewRes {
  scheme: SchemeBaseIncludesDto
}

export class SchemeCustomListBodyDto {
  @IsOptional()
  @IsString()
  friendlySchemeId?: string

  @IsOptional()
  @IsString()
  titleSearchText?: string

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  groupIds?: string[]

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  bundleIds?: string[]

  @IsOptional()
  @IsArray()
  @IsBoolean({ each: true })
  activeStates?: boolean[]

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @Transform(({ value }) => (Array.isArray(value) ? value.map(Number) : value))
  taggingIds?: number[]

  @IsOptional()
  @IsEnum(ContentType)
  @ApiPropertyOptional({ enum: ContentType })
  contentType?: ContentType
}

class DraftScheme
  implements
    Prisma.SchemeGetPayload<{
      select: {
        parentId: true
        updatedBy: true
      }
    }>
{
  parentId: string | null
  updatedBy: string
}

export class DraftSchemeWithEditorInfo extends DraftScheme {
  editorName?: string
  editorEmail?: string
}

class SchemeDtoForCustomListRes
  extends SchemeBaseDto
  implements
    Prisma.SchemeGetPayload<{
      include: {
        _count: true
        groups: { include: { group: true } }
      }
    }>
{
  _count: Prisma.SchemeCountOutputType & { eligibility: number } // Added  "{ eligibility: number }" due to Prisma.SchemeCountOutputType not generating correctly for optional fields
  groups: GroupsOnSchemesIncludeGroupDto[]
}
class SchemeCustomListResMeta {
  total: number
}
export class SchemeCustomListRes {
  @ValidateNested({ each: true })
  @Type(() => SchemeDtoForCustomListRes)
  @IsArray()
  records: SchemeDtoForCustomListRes[]

  meta: SchemeCustomListResMeta

  @ValidateNested({ each: true })
  @Type(() => DraftScheme)
  @IsArray()
  draftSchemes: DraftSchemeWithEditorInfo[]
}

export class SchemePagiationQueryDto {
  @IsPositive()
  @Type(() => Number)
  currentPage: number

  @IsPositive()
  @Type(() => Number)
  pageSize: number
}

// SchemeController#create
export class SchemeCreateBodyDto {
  @MaxLength(200, { message: 'Title can only have a max length of 200' })
  title: string

  @IsNotEmpty()
  listingAdmin: string

  @IsNotEmpty()
  @IsArray()
  listingOwners: string[]
}

export class SchemeCreateRes {
  createdScheme: SchemeBaseDto
  draftId: string
}

// SchemeController#delete
export class SchemeDeleteResDto {
  record: SchemeBaseDto
}
// SchemeController#update
class TranslatedHighlights {
  translationId: number

  @IsArray()
  @Length(0, LONG_TEXT_MAX_LENGTH, {
    each: true,
    message: `Highlight cannot be longer than ${LONG_TEXT_MAX_LENGTH} characters.`
  })
  translatedHighlights: string[]

  isOtherTranslationFieldsEmpty: boolean
}

export enum SchemeTabName {
  Basic_info = 'Basic_info',
  Scheme_details = 'Scheme_details',
  Content_criteria_tags = 'Content_criteria_tags',
  Translation = 'Translation',
  Settings = 'Settings'
}

export class TranslationType
  implements
    Omit<
      SchemeTranslation,
      'id' | 'createdAt' | 'createdBy' | 'updatedAt' | 'updatedBy' | 'updatedBy'
    >
{
  @IsString()
  title: string

  @IsString()
  description: string

  @IsArray()
  @Length(0, LONG_TEXT_MAX_LENGTH, {
    each: true,
    message: `Highlight cannot be longer than ${LONG_TEXT_MAX_LENGTH} characters.`
  })
  highlights: string[]

  @IsString()
  whatAreBenefits: string | null

  @IsString()
  whoIsEligible: string | null

  @IsString()
  howToApply: string | null

  @IsString()
  agencyWebsite: string | null

  @IsString()
  phoneNumber: string | null

  @IsString()
  emailOrForm: string | null

  @IsString()
  address: string | null

  @IsEnum(Language)
  @ApiProperty({ enum: Language })
  lang: Language

  @IsString()
  schemeId: string
}
export class SchemeUpdateBodyDto {
  // Tab 1
  @IsOptional()
  @MaxLength(200, { message: 'Title can only have a max length of 200' })
  title?: string

  @IsOptional()
  @IsArray()
  schemeOwners?: string[]

  @IsOptional()
  listingAdminId?: string

  // Tab 2
  @IsOptional()
  description?: string

  @IsOptional()
  whatAreBenefits?: string | null

  @IsOptional()
  whoIsEligible?: string | null

  @IsOptional()
  howToApply?: string | null

  @IsOptional()
  agencyWebsite?: string

  @IsOptional()
  contactNo?: string

  @IsOptional()
  emailOrForm?: string

  @IsOptional()
  address?: string

  @IsOptional()
  @Length(1, LONG_TEXT_MAX_LENGTH, {
    each: true,
    message: `Highlight cannot be longer than ${LONG_TEXT_MAX_LENGTH} characters.`
  })
  highlights?: string[]

  @ValidateIf((o: SchemeUpdateBodyDto) => o.applicationUrl !== '')
  @IsUrl({ message: 'Application url must be a valid url' })
  @MaxLength(300, {
    message: 'Application url can only have a max length of 300'
  })
  @IsOptional()
  applicationUrl?: string | null

  @IsOptional()
  meta?: string

  @IsOptional()
  translatedHighlights?: TranslatedHighlights[]

  // Tab 3
  @IsOptional()
  categoryIds?: string[]

  @IsOptional()
  taggingIds?: number[]

  @IsOptional()
  bundles?: string[]

  // Tab 4
  @IsOptional()
  @Type(() => TranslationType)
  translatedContent?: TranslationType

  // Tab 5
  @IsOptional()
  active?: boolean

  @IsOptional()
  validityStartDate?: string

  @IsOptional()
  validityEndDate?: string

  /**
   * validate when tabName is `Settings`
   *
   * 1. value should be either 'url' or 'email'
   */
  @ValidateIf((o: SchemeUpdateBodyDto) => o.tabName === SchemeTabName.Settings)
  @IsEnum(ContactType, {
    message: "Contact type should be either 'url' or 'email'"
  })
  contactType?: string

  /**
   * validate when tabName is `Settings`
   *
   * 1. value should follow url format if contactType is 'url'
   * 2. value should follow email format if contactType is 'email'
   */
  @ValidateIf((o: SchemeUpdateBodyDto) => o.tabName === SchemeTabName.Settings)
  @CustomValidateIf(
    (o: SchemeUpdateBodyDto) => o.contactType === ContactType.Email,
    isEmail,
    undefined,
    {
      message: 'Contact detail should be a valid email address'
    }
  )
  @CustomValidateIf(
    (o: SchemeUpdateBodyDto) => o.contactType === ContactType.Url,
    isURL,
    { protocols: ['https'], require_protocol: true },
    {
      message: 'Contact detail should be a valid url with protocol ‘https://’'
    }
  )
  contactDetail?: string

  // Other
  @IsEnum(SchemeTabName)
  @ApiProperty({ enum: SchemeTabName })
  tabName: SchemeTabName
}
export class SchemeUpdateRes {
  updatedScheme: SchemeBaseDto
}
