import { Injectable } from '@nestjs/common'
import { EligibilityHelper } from 'src/eligibility/eligibility.helper'
import { EligibilityProps } from 'src/types'

import {
  DEFAULT_MEMBER_HAS_MORE_THAN_ONE_PROPERTY_VALUE,
  getPayoutResults,
  OWN_IDENTITY,
  PayoutDetail
} from './bc.service.helper'
import { BC_SCHEMES } from './data/schemes'
import { GetPayoutReq } from './dto/GetPayoutReq.dto'
import { GetPayoutRes } from './dto/GetPayoutRes.dto'
import { BudgetPayoutType } from './types'

/*
 * This endpoint is ported over from a separate bc backend repo
 */

@Injectable()
export class BCService {
  constructor(
    private readonly eligibilityService: EligibilityHelper<
      { friendlyId: string } & BudgetPayoutType & EligibilityProps
    >
  ) {}

  checkPayoutEligibility(body: GetPayoutReq): GetPayoutRes {
    const { householdMembers, lang, ...rest } = body
    const finalResults: GetPayoutRes = {
      individual: [],
      household: []
    }
    // For each scheme
    BC_SCHEMES.forEach(
      ({
        schemeId,
        schemeCode,
        benefits,
        name,
        translations,
        modal,
        isUpdated
      }) => {
        // Handle translations
        const schemeName = translations?.[lang]?.name
          ? translations[lang].name
          : name

        // HOUSEHOLD
        if (benefits.benefitType === 'Household') {
          const answers = { ...rest }
          const hhPayouts = getPayoutResults(
            benefits.years as PayoutDetail[],
            schemeCode,
            answers,
            null,
            this.eligibilityService
          )
          if (hhPayouts.length > 0) {
            finalResults.household.push({
              description: benefits.description,
              benefitType: benefits.benefitType,
              payout: hhPayouts,
              schemeName,
              schemeId,
              schemeCode,
              lang,
              modal,
              isUpdated
            })
          }
        }
        // INDIVIDUAL
        else if (benefits.benefitType === 'Individual') {
          // For each household members
          const individualPayouts = householdMembers
            .map(
              ({ yearOfBirth, assessableIncome, identity, ...memberRest }) => {
                const answers = {
                  yearOfBirth,
                  assessableIncome,
                  ...rest,
                  ...memberRest,
                  ownMoreThanOneProperty:
                    identity === OWN_IDENTITY
                      ? rest.ownMoreThanOneProperty
                      : DEFAULT_MEMBER_HAS_MORE_THAN_ONE_PROPERTY_VALUE
                }
                const payoutForEachYear = getPayoutResults(
                  benefits.years as PayoutDetail[],
                  schemeCode,
                  answers,
                  benefits.referenceYearMap as { [key: string]: number },
                  this.eligibilityService
                )
                return {
                  identity,
                  yearsAndAmount: payoutForEachYear
                }
              }
            )
            .filter(({ yearsAndAmount }) => yearsAndAmount.length > 0)

          if (individualPayouts.length > 0) {
            finalResults.individual.push({
              description: benefits.description,
              benefitType: benefits.benefitType,
              payout: individualPayouts,
              schemeName,
              schemeId,
              schemeCode,
              lang,
              modal,
              isUpdated
            })
          }
        }
      }
    )
    return finalResults
  }
}
