import {
  AGE_1_TO_6,
  AGE_1_TO_12,
  AGE_13_TO_16,
  AGE_17_TO_20,
  AGE_21_TO_59,
  AGE_52_TO_75,
  AGE_EQUAL_TO_0,
  AGE_GREATER_THAN_EQUAL_TO_18,
  AGE_GREATER_THAN_EQUAL_TO_60,
  AGE_MORE_THAN_OR_EQUAL_21,
  ANNUAL_VALUE_TIER_1,
  ANNUAL_VALUE_TIER_2,
  ASSESSABLE_INCOME_TIER_1,
  ASSESSABLE_INCOME_TIER_2,
  ASSESSABLE_INCOME_TIER_3,
  ASSESSABLE_INCOME_TIER_4,
  MEDISAVE_BALANCE_TIER_1,
  PROPERTY_ONE_OR_ZERO,
  QUESTION_BEAR_YES
} from './schemes-criteria-data'

export const KACHING_ONE = {
  schemeId: 23,
  schemeCode: 'Q1UpgN0i',
  name: 'CPF Top-up - Large Family MediSave Grant',
  isUpdated: false,
  modal: 'cpf-top-up-from',
  translations: {
    zh: {
      name: '中央公积金填补 - 多子女家庭保健储蓄津贴'
    },
    ms: {
      name: 'Tokokan CPF – Geran MediSave Keluarga Besar'
    },
    ta: {
      name: 'மசேநி நிரப்புத்தொகை - கூடுதல் மெடிசேவ் போனஸ்'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 2
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 1
        }
      ],
      '2027': [
        {
          payoutYear: 2027,
          payoutMonth: 1
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [AGE_EQUAL_TO_0, QUESTION_BEAR_YES]
        },
        value: {
          '2025': 5000,
          '2026': 5000,
          '2027': 5000
        },
        unit: 'currency'
      }
    ]
  }
}

export const KACHING_TWO = {
  schemeId: 24,
  schemeCode: 'Q1UpgN0i',
  name: 'Top-up - Increased Child Development Account First Step Grant',
  isUpdated: false,
  modal: 'top-up-from',
  translations: {
    zh: {
      name: '填补 - 增加儿童培育户头起步津贴'
    },
    ms: {
      name: 'Tokokan – Geran Langkah Pertama Akaun Pembangunan Anak Ditambah'
    },
    ta: {
      name: 'நிரப்புத்தொகை - கூடுதல் பிள்ளை மேம்பாட்டுக் கணக்கு தொடக்க மானியம்'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 2
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 1
        }
      ],
      '2027': [
        {
          payoutYear: 2027,
          payoutMonth: 1
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [AGE_EQUAL_TO_0, QUESTION_BEAR_YES]
        },
        value: {
          '2025': 10000,
          '2026': 10000,
          '2027': 10000
        },
        unit: 'currency'
      }
    ]
  }
}

export const KACHING_THREE = {
  schemeId: 25,
  schemeCode: 'Q1UpgN0i',
  name: 'Credit - Large Family LifeSG Credits',
  isUpdated: false,
  modal: 'credit',
  translations: {
    zh: {
      name: '补助券 - 多子女家庭SG生活助手补助券'
    },
    ms: {
      name: 'Kredit – Kredit Life SG Keluarga Besar'
    },
    ta: {
      name: 'சிறப்புத்தொகை - பெரிய குடும்பத்திற்கான LifeSG சிறப்புத்தொகை'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 9
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 4
        }
      ],
      '2027': [
        {
          payoutYear: 2027,
          payoutMonth: 4
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [AGE_1_TO_6, QUESTION_BEAR_YES]
        },
        value: {
          '2025': 1000,
          '2026': 1000,
          '2027': 1000
        },
        unit: 'currency'
      }
    ]
  }
}

export const CREDIT_APPLE_CREDITS = {
  schemeId: 26,
  schemeCode: 'GNAgZJDj',
  name: 'Credit - Child LifeSG Credits',
  isUpdated: false,
  modal: 'credit',
  translations: {
    zh: {
      name: '补助券 - 育儿SG生活助手补助券'
    },
    ms: {
      name: 'Kredit – Kredit LifeSG Anak'
    },
    ta: {
      name: 'சிறப்புத்தொகை - பிள்ளைக்கான LifeSG சிறப்புத்தொகை'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 7
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 4
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: {
      '2025': 2025,
      '2026': 2025
    },
    years: [
      {
        eligibility: {
          all: [AGE_1_TO_12]
        },
        value: {
          '2025': 500
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [AGE_EQUAL_TO_0]
        },
        value: {
          '2026': 500
        },
        unit: 'currency'
      }
    ]
  }
}

export const TOP_UP_ORANGE_ACCOUNT = {
  schemeId: 27,
  schemeCode: 'vDzmk1wu',
  name: 'Top-up - Edusave Account',
  isUpdated: false,
  modal: 'top-up',
  translations: {
    zh: {
      name: '填补 - 教育储蓄户头'
    },
    ms: {
      name: 'Tokokan – Akaun Edusave'
    },
    ta: {
      name: 'நிரப்புத்தொகை - எடுசேவ் கணக்கு'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 7
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [AGE_13_TO_16]
        },
        value: {
          '2025': 500
        },
        unit: 'currency'
      }
    ]
  }
}

export const TOP_UP_PEAR_ACCOUNT = {
  schemeId: 28,
  schemeCode: 'vDzmk1wu',
  name: 'Top-up - Post-Secondary Education Account',
  isUpdated: false,
  modal: 'top-up',
  translations: {
    zh: {
      name: '填补- 中学后延续教育户头'
    },
    ms: {
      name: 'Tokokan – Akaun Pendidikan Posmenengah'
    },
    ta: {
      name: 'நிரப்புத்தொகை - உயர்நிலைப்பள்ளிக்குப் பிந்தைய கல்விக்கணக்கு'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 7
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [AGE_17_TO_20]
        },
        value: {
          '2025': 500
        },
        unit: 'currency'
      }
    ]
  }
}

export const CREDIT_JUMBO_CRAB = {
  schemeId: 29,
  schemeCode: 'ckLFmJ4I',
  name: 'Credit - SG Culture Pass Credits',
  isUpdated: false,
  modal: 'credit',
  translations: {
    zh: {
      name: '积分 - 新加坡文化通行证积分'
    },
    ms: {
      name: 'Kredit – Kredit Pas Budaya SG'
    },
    ta: {
      name: 'சிறப்புத்தொகை - எஸ்ஜி கலாசார சிறப்புத்தொகை'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 9
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [AGE_GREATER_THAN_EQUAL_TO_18]
        },
        value: {
          '2025': 100
        },
        unit: 'currency'
      }
    ]
  }
}

export const CREDIT_LAKSA_CREDITS = {
  schemeId: 30,
  schemeCode: 'ACTIVESG_CREDITS',
  name: 'Credit - SG60 ActiveSG Credit Top-Up',
  isUpdated: false,
  modal: 'credit',
  translations: {
    zh: {
      name: '积分 - 建国60周年ActiveSG 积分填补'
    },
    ms: {
      name: 'Kredit – Tokokan Kredit ActiveSG SG60'
    },
    ta: {
      name: 'சிறப்புத்தொகை - எஸ்ஜி60 ActiveSG கூடுதல் சிறப்புத்தொகை'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 6
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {},
        value: {
          '2025': 100
        },
        unit: 'currency'
      }
    ]
  }
}

export const VOUCHER_MILO_VOUCHERS = {
  schemeId: 31,
  schemeCode: 'BX7Se4Pg',
  name: 'Voucher - SG60 Vouchers',
  isUpdated: false,
  modal: 'voucher',
  translations: {
    zh: {
      name: '邻里购物券 - 建国60周年邻里购物券 '
    },
    ms: {
      name: 'Baucar – Baucar SG60'
    },
    ta: {
      name: 'பற்றுச்சீட்டு - எஸ்ஜி60 பற்றுச்சீட்டுகள் '
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 7
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [AGE_21_TO_59]
        },
        value: {
          '2025': 600
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [AGE_GREATER_THAN_EQUAL_TO_60]
        },
        value: {
          '2025': 800
        },
        unit: 'currency'
      }
    ]
  }
}

export const REBATE_MEEPOK = {
  schemeId: 32,
  schemeCode: 'PITR',
  name: 'Rebate - SG60 Personal Income Tax Rebate',
  isUpdated: false,
  modal: 'rebate-yoa',
  translations: {
    zh: {
      name: '回扣 - 建国60周年个人所得税回扣'
    },
    ms: {
      name: 'Rebat – Rebat Cukai Pendapatan Peribadi SG60'
    },
    ta: {
      name: 'தள்ளுபடி - எஸ்ஜி60 தனிநபர் வருமான வரி தள்ளுபடி'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 5
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21, // Tax rebate is only shown to users >= 21 as only they normally pay income tax
            {
              any: [
                ASSESSABLE_INCOME_TIER_1,
                ASSESSABLE_INCOME_TIER_2,
                ASSESSABLE_INCOME_TIER_3,
                ASSESSABLE_INCOME_TIER_4
              ]
            }
          ]
        },
        value: {
          '2025': 200
        },
        unit: 'currency',
        type: 'max'
      }
    ]
  }
}

export const CPF_TOP_UP_ADDITIONAL_MEDISAVE_BONUS = {
  schemeId: 33,
  schemeCode: 'cPaqWRKy',
  name: 'CPF Top-up - Additional MediSave Bonus',
  isUpdated: false,
  modal: 'cpf-top-up',
  translations: {
    zh: {
      name: '中央公积金填补 - 保健储蓄额外花红'
    },
    ms: {
      name: 'Tokokan CPF – Bonus MediSave Tambahan'
    },
    ta: {
      name: 'மசேநி நிரப்புத்தொகை - கூடுதல் மெடிசேவ் போனஸ்'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 7
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [
            AGE_52_TO_75,
            PROPERTY_ONE_OR_ZERO,
            MEDISAVE_BALANCE_TIER_1,
            { any: [ANNUAL_VALUE_TIER_1, ANNUAL_VALUE_TIER_2] }
          ]
        },
        value: {
          '2025': 500
        },
        unit: 'currency'
      }
    ]
  }
}

export const VOUCHER_HEAL_VOUCHERS = {
  schemeId: 34,
  schemeCode: '9kUw7c5Q',
  name: 'Voucher - Climate Vouchers',
  isUpdated: false,
  modal: 'voucher-from',
  translations: {
    zh: {
      name: '优惠券 - 气候优惠券'
    },
    ms: {
      name: 'Baucar - Baucar Iklim'
    },
    ta: {
      name: 'பற்றுச்சீட்டுகள் - பருவநிலைப் பற்றுச்சீட்டுகள்'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 4
        }
      ]
    },
    benefitType: 'Household',
    referenceYearMap: null,
    years: [
      {
        eligibility: {},
        value: {
          '2025': 400
        },
        unit: 'currency'
      }
    ]
  }
}
