import { MAJULAH_EARN_AND_SAVE } from './schemes-bc24'
import {
  CPF_TOP_UP_ADDITIONAL_MEDISAVE_BONUS,
  CREDIT_APPLE_CREDITS,
  CREDIT_JUMBO_CRAB,
  CREDIT_LAKSA_CREDITS,
  K<PERSON><PERSON>ING_ONE,
  K<PERSON><PERSON><PERSON>_THREE,
  KACHING_TWO,
  REBATE_MEEPOK,
  TOP_UP_ORANGE_ACCOUNT,
  TOP_UP_PEAR_ACCOUNT,
  VOUCHER_HEAL_VOUCHERS,
  VOUCHER_MILO_VOUCHERS
} from './schemes-bc25'
import {
  AGE_55_TO_64,
  AGE_65_TO_74,
  AGE_75_TO_84,
  AGE_LESS_THAN_OR_EQUAL_TO_20,
  AGE_MORE_THAN_OR_EQUAL_21,
  AGE_MORE_THAN_OR_EQUAL_85,
  AGE_MORE_THAN_OR_EQUAL_TO_55,
  AGE_MORE_THAN_OR_EQUAL_TO_65,
  ANNUAL_VALUE_TIER_1,
  ANNUAL_VALUE_TIER_2,
  ASSESSABLE_INCOME_TIER_1,
  ASSESSABLE_INCOME_TIER_2,
  ASSESSABLE_INCOME_TIER_3,
  ASSESSABLE_INCOME_TIER_4,
  HOUSING_TYPE_1R,
  HOUSING_TYPE_2R,
  HOUSING_TYPE_3R,
  HOUSING_TYPE_4R,
  HOUSING_TYPE_5R,
  HOUSING_TYPE_EX_FLAT,
  PROPERTY_ABOVE_ONE,
  PROPERTY_ONE_OR_ZERO
} from './schemes-criteria-data'

const CASH_ASSURANCE_PACKAGE = {
  schemeId: 1,
  schemeCode: 'GST-CASH',
  name: 'Cash - Assurance Package',
  isUpdated: false,
  modal: 'cash',
  translations: {
    zh: {
      name: '现金补助 - 定心与援助配套'
    },
    ms: {
      name: 'Tunai - Pakej Jaminan'
    },
    ta: {
      name: 'ரொக்கம் - உத்தரவாதத் தொகுப்புத்திட்டம்'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 12
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 12
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: {
      '2025': 2026,
      '2026': 2027
    },
    years: [
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ONE_OR_ZERO,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 600,
          '2026': 250
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ONE_OR_ZERO,
            ASSESSABLE_INCOME_TIER_2
          ]
        },
        value: {
          '2025': 600,
          '2026': 250
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ONE_OR_ZERO,
            ASSESSABLE_INCOME_TIER_3
          ]
        },
        value: {
          '2025': 350,
          '2026': 150
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ONE_OR_ZERO,
            ASSESSABLE_INCOME_TIER_4
          ]
        },
        value: {
          '2025': 100,
          '2026': 100
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ABOVE_ONE,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 100,
          '2026': 100
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ABOVE_ONE,
            ASSESSABLE_INCOME_TIER_2
          ]
        },
        value: {
          '2025': 100,
          '2026': 100
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ABOVE_ONE,
            ASSESSABLE_INCOME_TIER_3
          ]
        },
        value: {
          '2025': 100,
          '2026': 100
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ABOVE_ONE,
            ASSESSABLE_INCOME_TIER_4
          ]
        },
        value: {
          '2025': 100,
          '2026': 100
        },
        unit: 'currency'
      }
    ]
  }
}

const CASH_ASSURANCE_PACKAGE_SENIORS_BONUS = {
  schemeId: 2,
  schemeCode: 'GST-SENIORS',
  name: "Cash - Assurance Package Seniors' Bonus",
  isUpdated: false,
  modal: 'cash',
  translations: {
    zh: {
      name: '现金补助 - 定心与援助配套乐龄花红'
    },
    ms: {
      name: 'Tunai - Pakej Jaminan Bonus Warga Emas'
    },
    ta: {
      name: 'ரொக்கம் - உத்தரவாதத் தொகுப்புத்திட்ட மூத்தோருக்கான ஊக்கத்தொகை'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 2
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [
            AGE_55_TO_64,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_1,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 250
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_55_TO_64,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_2,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 200
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_TO_65,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_1,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 300
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_TO_65,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_2,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 200
        },
        unit: 'currency'
      }
    ]
  }
}

const CASH_GSTV = {
  schemeId: 4,
  schemeCode: 'GSTV-CASH',
  name: 'Cash - GSTV',
  isUpdated: false,
  modal: 'cash',
  translations: {
    zh: {
      name: '现金补助 - 消费税补助券'
    },
    ms: {
      name: 'Tunai - Baucar GST'
    },
    ta: {
      name: 'ரொக்கம் - பொருள், சேவை வரிப் பற்றுச்சீட்டு'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 8
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 8
        }
      ],
      '2027': [
        {
          payoutYear: 2027,
          payoutMonth: 8
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_1,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 850,
          '2026': 850,
          '2027': 850
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_1,
            ASSESSABLE_INCOME_TIER_2
          ]
        },
        value: {
          '2025': 850,
          '2026': 850,
          '2027': 850
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_2,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 450,
          '2026': 450,
          '2027': 450
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_21,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_2,
            ASSESSABLE_INCOME_TIER_2
          ]
        },
        value: {
          '2025': 450,
          '2026': 450,
          '2027': 450
        },
        unit: 'currency'
      }
    ]
  }
}

const MEDISAVE_ASSURANCE_PACKAGE = {
  schemeId: 5,
  schemeCode: 'MEDISAVE-TOPUP',
  name: 'CPF Top-up - Assurance Package MediSave',
  isUpdated: false,
  modal: 'cpf-top-up',
  translations: {
    zh: {
      name: '保健储蓄 - 定心与援助配套'
    },
    ms: {
      name: 'Medisave - Pakej Jaminan'
    },
    ta: {
      name: 'மெடிசேவ் - உத்தரவாதத் தொகுப்புத்திட்டம்'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 2
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          any: [AGE_LESS_THAN_OR_EQUAL_TO_20, AGE_MORE_THAN_OR_EQUAL_TO_55]
        },
        value: {
          '2025': 150
        },
        unit: 'currency'
      }
    ]
  }
}

const MEDISAVE_GSTV = {
  schemeId: 6,
  schemeCode: 'GSTV-MEDISAVE',
  name: 'CPF Top-up - GSTV — MediSave',
  isUpdated: false,
  modal: 'cpf-top-up',
  translations: {
    zh: {
      name: '保健储蓄 - 消费税补助券'
    },
    ms: {
      name: 'Medisave - Baucar GST'
    },
    ta: {
      name: 'மெடிசேவ் - பொருள், சேவை வரிப் பற்றுச்சீட்டு'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 8
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 8
        }
      ],
      '2027': [
        {
          payoutYear: 2027,
          payoutMonth: 8
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [AGE_65_TO_74, PROPERTY_ONE_OR_ZERO, ANNUAL_VALUE_TIER_1]
        },
        value: {
          '2025': 250,
          '2026': 250,
          '2027': 250
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [AGE_65_TO_74, PROPERTY_ONE_OR_ZERO, ANNUAL_VALUE_TIER_2]
        },
        value: {
          '2025': 150,
          '2026': 150,
          '2027': 150
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [AGE_75_TO_84, PROPERTY_ONE_OR_ZERO, ANNUAL_VALUE_TIER_1]
        },
        value: {
          '2025': 350,
          '2026': 350,
          '2027': 350
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [AGE_75_TO_84, PROPERTY_ONE_OR_ZERO, ANNUAL_VALUE_TIER_2]
        },
        value: {
          '2025': 250,
          '2026': 250,
          '2027': 250
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_85,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_1
          ]
        },
        value: {
          '2025': 450,
          '2026': 450,
          '2027': 450
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_85,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_2
          ]
        },
        value: {
          '2025': 350,
          '2026': 350,
          '2027': 350
        },
        unit: 'currency'
      }
    ]
  }
}

const REBATE_GSTV_U_SAVE = {
  schemeId: 13,
  schemeCode: 'U-SAVE',
  name: 'Rebate - U-Save',
  isUpdated: false,
  modal: 'rebate',
  translations: {
    zh: {
      name: '回扣 - 费税补助卷计划 – 水电费回扣'
    },
    ms: {
      name: 'Rebat - Baucar GST – U-Save'
    },
    ta: {
      name: 'கழிவு - பொருள், சேவை வரிப் பற்றுச்சீட்டு – யு-சேவ்'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 1
        },
        {
          payoutYear: 2025,
          payoutMonth: 4
        },
        {
          payoutYear: 2025,
          payoutMonth: 7
        },
        {
          payoutYear: 2025,
          payoutMonth: 10
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 1
        },
        {
          payoutYear: 2026,
          payoutMonth: 4
        },
        {
          payoutYear: 2026,
          payoutMonth: 7
        },
        {
          payoutYear: 2026,
          payoutMonth: 10
        }
      ],
      '2027': [
        {
          payoutYear: 2027,
          payoutMonth: 1
        },
        {
          payoutYear: 2027,
          payoutMonth: 4
        },
        {
          payoutYear: 2027,
          payoutMonth: 7
        },
        {
          payoutYear: 2027,
          payoutMonth: 10
        }
      ]
    },
    benefitType: 'Household',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_1R]
        },
        value: {
          '2025': 855,
          '2026': 475,
          '2027': 380
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_2R]
        },
        value: {
          '2025': 855,
          '2026': 475,
          '2027': 380
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_3R]
        },
        value: {
          '2025': 765,
          '2026': 425,
          '2027': 340
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_4R]
        },
        value: {
          '2025': 675,
          '2026': 375,
          '2027': 300
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_5R]
        },
        value: {
          '2025': 585,
          '2026': 325,
          '2027': 260
        },
        unit: 'currency'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_EX_FLAT]
        },
        value: {
          '2025': 495,
          '2026': 275,
          '2027': 220
        },
        unit: 'currency'
      }
    ]
  }
}

const VOUCHER_CDC_VOUCHERS = {
  schemeId: 14,
  schemeCode: 'CDC',
  name: 'Voucher - CDC Vouchers',
  isUpdated: false,
  modal: 'voucher',
  translations: {
    zh: {
      name: '补助券 - 社理会邻里购物券'
    },
    ms: {
      name: 'Baucar - Baucar CDC'
    },
    ta: {
      name: 'பற்றுச்சீட்டுகள் - சமூக மேம்பாட்டு மன்றப் பற்றுச்சீட்டுகள்'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 1
        },
        {
          payoutYear: 2025,
          payoutMonth: 5
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 1
        }
      ]
    },
    benefitType: 'Household',
    referenceYearMap: null,
    years: [
      {
        eligibility: {},
        value: {
          '2025': 800,
          '2026': 300
        },
        unit: 'currency'
      }
    ]
  }
}

const REBATE_GSTV_SERVICE_AND_CONSERVANCY_CHARGES_REBATES = {
  schemeId: 15,
  schemeCode: 'SCCR',
  name: 'Rebate - Service and Conservancy Charges (S&CC) Rebate',
  isUpdated: false,
  modal: 'rebate',
  translations: {
    zh: {
      name: '回扣 - 组屋杂费（S&CC）回扣'
    },
    ms: {
      name: 'Rebat - Rebat Bayaran Perkhidmatan dan Penyenggaraan (S&CC)'
    },
    ta: {
      name: 'தள்ளுபடி - சேவை, பராமரிப்புக் கட்டணத் தள்ளுபடிகள் (S&CC)'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 1
        },
        {
          payoutYear: 2025,
          payoutMonth: 4
        },
        {
          payoutYear: 2025,
          payoutMonth: 7
        },
        {
          payoutYear: 2025,
          payoutMonth: 10
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 1,
          exclude: [{ option: 'EXECUTIVE_MULTI_GEN_FLAT', code: 'HT' }]
        },
        {
          payoutYear: 2026,
          payoutMonth: 4
        },
        {
          payoutYear: 2026,
          payoutMonth: 7
        },
        {
          payoutYear: 2026,
          payoutMonth: 10
        }
      ],
      '2027': [
        {
          payoutYear: 2027,
          payoutMonth: 1,
          exclude: [{ option: 'EXECUTIVE_MULTI_GEN_FLAT', code: 'HT' }]
        },
        {
          payoutYear: 2027,
          payoutMonth: 4
        },
        {
          payoutYear: 2027,
          payoutMonth: 7
        },
        {
          payoutYear: 2027,
          payoutMonth: 10
        }
      ]
    },
    benefitType: 'Household',
    referenceYearMap: null,
    years: [
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_1R]
        },
        value: {
          '2025': 4,
          '2026': 3.5,
          '2027': 3.5
        },
        unit: 'months'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_2R]
        },
        value: {
          '2025': 4,
          '2026': 3.5,
          '2027': 3.5
        },
        unit: 'months'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_3R]
        },
        value: {
          '2025': 3,
          '2026': 2.5,
          '2027': 2.5
        },
        unit: 'months'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_4R]
        },
        value: {
          '2025': 3,
          '2026': 2.5,
          '2027': 2.5
        },
        unit: 'months'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_5R]
        },
        value: {
          '2025': 2.5,
          '2026': 2,
          '2027': 2
        },
        unit: 'months'
      },
      {
        eligibility: {
          all: [PROPERTY_ONE_OR_ZERO, HOUSING_TYPE_EX_FLAT]
        },
        value: {
          '2025': 2,
          '2026': 1.5,
          '2027': 1.5
        },
        unit: 'months'
      }
    ]
  }
}

export const BC_SCHEMES = [
  CASH_ASSURANCE_PACKAGE,
  CASH_ASSURANCE_PACKAGE_SENIORS_BONUS,
  CASH_GSTV,
  MEDISAVE_ASSURANCE_PACKAGE,
  MEDISAVE_GSTV,
  REBATE_GSTV_U_SAVE,
  VOUCHER_CDC_VOUCHERS,
  REBATE_GSTV_SERVICE_AND_CONSERVANCY_CHARGES_REBATES,
  MAJULAH_EARN_AND_SAVE,
  KACHING_ONE,
  KACHING_TWO,
  KACHING_THREE,
  CREDIT_APPLE_CREDITS,
  TOP_UP_ORANGE_ACCOUNT,
  TOP_UP_PEAR_ACCOUNT,
  CREDIT_JUMBO_CRAB,
  CREDIT_LAKSA_CREDITS,
  VOUCHER_MILO_VOUCHERS,
  REBATE_MEEPOK,
  CPF_TOP_UP_ADDITIONAL_MEDISAVE_BONUS,
  VOUCHER_HEAL_VOUCHERS
]
