import {
  AGE_MORE_THAN_OR_EQUAL_51,
  ANNUAL_VALUE_TIER_1,
  ANNUAL_VALUE_TIER_2,
  ASSESSABLE_INCOME_TIER_1,
  ASSESSABLE_INCOME_TIER_2,
  ASSESSABLE_INCOME_TIER_3,
  PROPERTY_ONE_OR_ZERO
} from './schemes-criteria-data'

export const MAJULAH_EARN_AND_SAVE = {
  schemeId: 22,
  schemeCode: 'MP-ESB',
  name: 'CPF Top-up - Majulah Package — Earn and Save Bonus',
  isUpdated: false,
  modal: 'cpf-top-up',
  translations: {
    zh: {
      name: '中央公积金填补-就业储蓄花红'
    },
    ms: {
      name: 'Tokokan CPF – <PERSON><PERSON> – Bonus “Earn and Save”'
    },
    ta: {
      name: 'மசேநி நிரப்புத்தொகை - மாஜுலா தொகுப்புத்திட்டம் - சம்பாதித்துச் சேமியுங்கள் போனஸ்'
    }
  },
  benefits: {
    description: {
      '2025': [
        {
          payoutYear: 2025,
          payoutMonth: 3
        }
      ],
      '2026': [
        {
          payoutYear: 2026,
          payoutMonth: 3
        }
      ],
      '2027': [
        {
          payoutYear: 2027,
          payoutMonth: 3
        }
      ]
    },
    benefitType: 'Individual',
    referenceYearMap: {
      '2025': 2024,
      '2026': 2024,
      '2027': 2024
    },
    years: [
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_51,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_1,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 1000,
          '2026': 1000,
          '2027': 1000
        },
        unit: 'currency',
        type: 'max'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_51,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_1,
            ASSESSABLE_INCOME_TIER_2
          ]
        },
        value: {
          '2025': 1000,
          '2026': 1000,
          '2027': 1000
        },
        unit: 'currency',
        type: 'max'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_51,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_1,
            ASSESSABLE_INCOME_TIER_3
          ]
        },
        value: {
          '2025': 1000,
          '2026': 1000,
          '2027': 1000
        },
        unit: 'currency',
        type: 'max'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_51,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_2,
            ASSESSABLE_INCOME_TIER_1
          ]
        },
        value: {
          '2025': 1000,
          '2026': 1000,
          '2027': 1000
        },
        unit: 'currency',
        type: 'max'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_51,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_2,
            ASSESSABLE_INCOME_TIER_2
          ]
        },
        value: {
          '2025': 1000,
          '2026': 1000,
          '2027': 1000
        },
        unit: 'currency',
        type: 'max'
      },
      {
        eligibility: {
          all: [
            AGE_MORE_THAN_OR_EQUAL_51,
            PROPERTY_ONE_OR_ZERO,
            ANNUAL_VALUE_TIER_2,
            ASSESSABLE_INCOME_TIER_3
          ]
        },
        value: {
          '2025': 1000,
          '2026': 1000,
          '2027': 1000
        },
        unit: 'currency',
        type: 'max'
      }
    ]
  }
}
