export const PROPERTY_ONE_OR_ZERO = {
  id: 'ownMoreThanOneProperty',
  type: 'value',
  value: 'No'
}
export const PROPERTY_ABOVE_ONE = {
  id: 'ownMoreThanOneProperty',
  type: 'value',
  value: 'Yes'
}

// ----------- New AI bands -----------
export const ASSESSABLE_INCOME_TIER_1 = {
  id: 'assessableIncome',
  type: 'value',
  value: 'ASSESSABLE_INCOME_TIER_1'
}

export const ASSESSABLE_INCOME_TIER_2 = {
  id: 'assessableIncome',
  type: 'value',
  value: 'ASSESSABLE_INCOME_TIER_2'
}

export const ASSESSABLE_INCOME_TIER_3 = {
  id: 'assessableIncome',
  type: 'value',
  value: 'ASSESSABLE_INCOME_TIER_3'
}

export const ASSESSABLE_INCOME_TIER_4 = {
  id: 'assessableIncome',
  type: 'value',
  value: 'ASSESSABLE_INCOME_TIER_4'
}

// ----------- New AV bands -----------
export const ANNUAL_VALUE_TIER_1 = {
  id: 'annualValue',
  type: 'value',
  value: 'ANNUAL_VALUE_TIER_1'
}

export const ANNUAL_VALUE_TIER_2 = {
  id: 'annualValue',
  type: 'value',
  value: 'ANNUAL_VALUE_TIER_2'
}
export const ANNUAL_VALUE_TIER_3 = {
  id: 'annualValue',
  type: 'value',
  value: 'ANNUAL_VALUE_TIER_3'
}
// ---------------------------------------

export const HOUSING_TYPE_1R = {
  id: 'housingType',
  type: 'value',
  value: 'PUBLIC_1R'
}
export const HOUSING_TYPE_2R = {
  id: 'housingType',
  type: 'value',
  value: 'PUBLIC_2R'
}
export const HOUSING_TYPE_3R = {
  id: 'housingType',
  type: 'value',
  value: 'PUBLIC_3R'
}
export const HOUSING_TYPE_4R = {
  id: 'housingType',
  type: 'value',
  value: 'PUBLIC_4R'
}
export const HOUSING_TYPE_5R = {
  id: 'housingType',
  type: 'value',
  value: 'PUBLIC_5R'
}
export const HOUSING_TYPE_EX_FLAT = {
  id: 'housingType',
  type: 'value',
  value: 'EXECUTIVE_MULTI_GEN_FLAT'
}

/* ------------ New Age Criterias ---------------- */

export const AGE_MORE_THAN_OR_EQUAL_21 = {
  id: 'age',
  type: 'threshold',
  value: 21,
  operator: 'greaterThan',
  inclusive: true
}

export const AGE_MORE_THAN_OR_EQUAL_TO_65 = {
  id: 'age',
  type: 'threshold',
  value: 65,
  operator: 'greaterThan',
  inclusive: true
}

export const AGE_MORE_THAN_OR_EQUAL_TO_55 = {
  id: 'age',
  type: 'threshold',
  value: 55,
  operator: 'greaterThan',
  inclusive: true
}

export const AGE_MORE_THAN_OR_EQUAL_85 = {
  id: 'age',
  type: 'threshold',
  value: 85,
  operator: 'greaterThan',
  inclusive: true
}

export const AGE_MORE_THAN_OR_EQUAL_51 = {
  id: 'age',
  type: 'threshold',
  value: 51,
  operator: 'greaterThan',
  inclusive: true
}

export const AGE_55_TO_64 = {
  id: 'age',
  type: 'range',
  min: 55,
  max: 64,
  inclusive: true
}

export const AGE_65_TO_74 = {
  id: 'age',
  type: 'range',
  min: 65,
  max: 74,
  inclusive: true
}

export const AGE_75_TO_84 = {
  id: 'age',
  type: 'range',
  min: 75,
  max: 84,
  inclusive: true
}

export const AGE_LESS_THAN_OR_EQUAL_TO_20 = {
  id: 'age',
  type: 'threshold',
  value: 20,
  operator: 'lessThan',
  inclusive: true
}

export const AGE_EQUAL_TO_0 = {
  id: 'age',
  type: 'value',
  value: 0
}

export const AGE_1_TO_6 = {
  id: 'age',
  type: 'range',
  min: 1,
  max: 6,
  inclusive: true
}

export const AGE_1_TO_12 = {
  id: 'age',
  type: 'range',
  min: 1,
  max: 12,
  inclusive: true
}

export const AGE_13_TO_16 = {
  id: 'age',
  type: 'range',
  min: 13,
  max: 16,
  inclusive: true
}

export const AGE_17_TO_20 = {
  id: 'age',
  type: 'range',
  min: 17,
  max: 20,
  inclusive: true
}

export const AGE_GREATER_THAN_EQUAL_TO_18 = {
  id: 'age',
  type: 'threshold',
  value: 18,
  operator: 'greaterThan',
  inclusive: true
}

export const AGE_21_TO_59 = {
  id: 'age',
  type: 'range',
  min: 21,
  max: 59,
  inclusive: true
}

export const AGE_52_TO_75 = {
  id: 'age',
  type: 'range',
  min: 52,
  max: 75,
  inclusive: true
}

export const AGE_GREATER_THAN_EQUAL_TO_60 = {
  id: 'age',
  type: 'threshold',
  value: 60,
  operator: 'greaterThan',
  inclusive: true
}

/* ------------------------------------------------ */

/* Budget 2025 New Questions */

export const QUESTION_BEAR_YES = {
  id: 'questionBear',
  type: 'value',
  value: 'Yes'
}

export const MEDISAVE_BALANCE_TIER_1 = {
  id: 'medisaveBalance',
  type: 'value',
  value: 'MEDISAVE_BALANCE_TIER_1'
}
