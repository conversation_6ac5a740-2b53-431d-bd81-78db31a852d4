import { Test, TestingModule } from '@nestjs/testing'
import _ from 'lodash'
import { EligibilityHelper } from 'src/eligibility/eligibility.helper'

import { BCService } from './bc.service'
import { GetPayoutReq } from './dto/GetPayoutReq.dto'
import {
  GetPayoutRes,
  HouseholdPayout,
  PayoutPeriod
} from './dto/GetPayoutRes.dto'

const HOUSING_1R: GetPayoutReq = {
  housingType: 'PUBLIC_1R', //latent factor
  ownMoreThanOneProperty: 'No', // only used by S&CC
  propertyOwnership: 'OWNED_BY_ME_OR_HOUSEHOLD_MEMBER',
  annualValue: 'ANNUAL_VALUE_TIER_1',
  householdMembers: [
    {
      yearOfBirth: 2001,
      assessableIncome: 'ASSESSABLE_INCOME_TIER_1',
      identity: 'You'
    }
  ],
  lang: 'en'
}

const HOUSING_2R: GetPayoutReq = updateRequestData(
  HOUSING_1R,
  'housingType',
  'PUBLIC_2R'
)

const HOUSING_3R: GetPayoutReq = updateRequestData(
  HOUSING_1R,
  'housingType',
  'PUBLIC_3R'
)

const HOUSING_4R: GetPayoutReq = updateRequestData(
  HOUSING_1R,
  'housingType',
  'PUBLIC_4R'
)

const HOUSING_5R: GetPayoutReq = updateRequestData(
  HOUSING_1R,
  'housingType',
  'PUBLIC_5R'
)

const HOUSING_EXEC: GetPayoutReq = updateRequestData(
  HOUSING_1R,
  'housingType',
  'EXECUTIVE_MULTI_GEN_FLAT'
)

const HOUSING_PRIVATE: GetPayoutReq = updateRequestData(
  HOUSING_1R,
  'housingType',
  'PRIVATE_PROPERTY'
)

function updateRequestData(
  data: GetPayoutReq,
  path: string,
  value: string | number
): GetPayoutReq {
  return _.set(_.cloneDeep(data), path, value)
}

function getHhPayoutAmount(
  result: GetPayoutRes,
  schemeId: number
): HouseholdPayout[] {
  return _.find(result.household, { schemeId })?.payout
}

function getHhPayoutPeriod(
  result: GetPayoutRes,
  schemeId: number
): { [key: string]: PayoutPeriod[] } {
  return _.find(result.household, { schemeId }).description
}

describe('BC Household Schemes', () => {
  let service: BCService

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BCService, EligibilityHelper]
    }).compile()

    service = module.get<BCService>(BCService)
  })

  describe('Rebate - U-Save (A-GST-U-SAVE)', () => {
    it('should return correct payment amount for housing 1R', () => {
      const result = service.checkPayoutEligibility(HOUSING_1R)

      const payoutAmount = [
        { year: '2025', amount: 855, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 475, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 380, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 13)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 2R', () => {
      const result = service.checkPayoutEligibility(HOUSING_2R)

      const payoutAmount = [
        { year: '2025', amount: 855, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 475, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 380, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 13)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 3R', () => {
      const result = service.checkPayoutEligibility(HOUSING_3R)

      const payoutAmount = [
        { year: '2025', amount: 765, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 425, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 340, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 13)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 4R', () => {
      const result = service.checkPayoutEligibility(HOUSING_4R)

      const payoutAmount = [
        { year: '2025', amount: 675, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 375, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 300, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 13)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 5R', () => {
      const result = service.checkPayoutEligibility(HOUSING_5R)

      const payoutAmount = [
        { year: '2025', amount: 585, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 325, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 260, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 13)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing exec', () => {
      const result = service.checkPayoutEligibility(HOUSING_EXEC)

      const payoutAmount = [
        { year: '2025', amount: 495, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 275, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 220, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 13)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 1R', () => {
      const result = service.checkPayoutEligibility(HOUSING_PRIVATE)

      expect(getHhPayoutAmount(result, 13)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(HOUSING_1R)

      const payoutPeriod = {
        '2025': [
          { payoutYear: 2025, payoutMonth: 1 },
          { payoutYear: 2025, payoutMonth: 4 },
          { payoutYear: 2025, payoutMonth: 7 },
          { payoutYear: 2025, payoutMonth: 10 }
        ],
        '2026': [
          { payoutYear: 2026, payoutMonth: 1 },
          { payoutYear: 2026, payoutMonth: 4 },
          { payoutYear: 2026, payoutMonth: 7 },
          { payoutYear: 2026, payoutMonth: 10 }
        ],
        '2027': [
          { payoutYear: 2027, payoutMonth: 1 },
          { payoutYear: 2027, payoutMonth: 4 },
          { payoutYear: 2027, payoutMonth: 7 },
          { payoutYear: 2027, payoutMonth: 10 }
        ]
      }
      expect(getHhPayoutPeriod(result, 13)).toEqual(payoutPeriod)
    })
  })

  describe('Voucher - CDC Vouchers (CDC)', () => {
    it('should return correct payment amount for housing 1R', () => {
      const result = service.checkPayoutEligibility(HOUSING_1R)

      const payoutAmount = [
        { year: '2025', amount: 800, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 300, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 14)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 2R', () => {
      const result = service.checkPayoutEligibility(HOUSING_2R)

      const payoutAmount = [
        { year: '2025', amount: 800, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 300, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 14)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 3R', () => {
      const result = service.checkPayoutEligibility(HOUSING_3R)

      const payoutAmount = [
        { year: '2025', amount: 800, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 300, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 14)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 4R', () => {
      const result = service.checkPayoutEligibility(HOUSING_4R)

      const payoutAmount = [
        { year: '2025', amount: 800, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 300, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 14)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 5R', () => {
      const result = service.checkPayoutEligibility(HOUSING_5R)

      const payoutAmount = [
        { year: '2025', amount: 800, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 300, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 14)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing exec', () => {
      const result = service.checkPayoutEligibility(HOUSING_EXEC)

      const payoutAmount = [
        { year: '2025', amount: 800, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 300, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 14)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing private', () => {
      const result = service.checkPayoutEligibility(HOUSING_PRIVATE)

      const payoutAmount = [
        { year: '2025', amount: 800, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 300, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 14)).toEqual(payoutAmount)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(HOUSING_1R)

      const payoutPeriod = {
        '2025': [
          { payoutYear: 2025, payoutMonth: 1 },
          { payoutYear: 2025, payoutMonth: 5 }
        ],
        '2026': [{ payoutYear: 2026, payoutMonth: 1 }]
      }
      expect(getHhPayoutPeriod(result, 14)).toEqual(payoutPeriod)
    })
  })

  describe('Rebate - Service and Conservancy Charges (S&CC) Rebate (SERVICE-CONSERVANCY-CHARGES)', () => {
    it('should return correct payment amount for housing 1R', () => {
      const result = service.checkPayoutEligibility(HOUSING_1R)

      const payoutAmount = [
        { year: '2025', amount: 4, unit: 'months', type: 'exact' },
        { year: '2026', amount: 3.5, unit: 'months', type: 'exact' },
        { year: '2027', amount: 3.5, unit: 'months', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 15)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 2R', () => {
      const result = service.checkPayoutEligibility(HOUSING_2R)

      const payoutAmount = [
        { year: '2025', amount: 4, unit: 'months', type: 'exact' },
        { year: '2026', amount: 3.5, unit: 'months', type: 'exact' },
        { year: '2027', amount: 3.5, unit: 'months', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 15)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 3R', () => {
      const result = service.checkPayoutEligibility(HOUSING_3R)

      const payoutAmount = [
        { year: '2025', amount: 3, unit: 'months', type: 'exact' },
        { year: '2026', amount: 2.5, unit: 'months', type: 'exact' },
        { year: '2027', amount: 2.5, unit: 'months', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 15)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 4R', () => {
      const result = service.checkPayoutEligibility(HOUSING_4R)

      const payoutAmount = [
        { year: '2025', amount: 3, unit: 'months', type: 'exact' },
        { year: '2026', amount: 2.5, unit: 'months', type: 'exact' },
        { year: '2027', amount: 2.5, unit: 'months', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 15)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 5R', () => {
      const result = service.checkPayoutEligibility(HOUSING_5R)

      const payoutAmount = [
        { year: '2025', amount: 2.5, unit: 'months', type: 'exact' },
        { year: '2026', amount: 2, unit: 'months', type: 'exact' },
        { year: '2027', amount: 2, unit: 'months', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 15)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing exec', () => {
      const result = service.checkPayoutEligibility(HOUSING_EXEC)

      const payoutAmount = [
        { year: '2025', amount: 2, unit: 'months', type: 'exact' },
        { year: '2026', amount: 1.5, unit: 'months', type: 'exact' },
        { year: '2027', amount: 1.5, unit: 'months', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 15)).toEqual(payoutAmount)
    })

    it('should not return payment amount for > 1 property', () => {
      const PROP_2: GetPayoutReq = updateRequestData(
        HOUSING_1R,
        'ownMoreThanOneProperty',
        'Yes'
      )
      const result = service.checkPayoutEligibility(PROP_2)

      expect(getHhPayoutAmount(result, 15)).toEqual(undefined)
    })

    it('should not return correct amount for private property', () => {
      const result = service.checkPayoutEligibility(HOUSING_PRIVATE)

      expect(getHhPayoutAmount(result, 15)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(HOUSING_1R)

      const payoutPeriod = {
        '2025': [
          {
            payoutYear: 2025,
            payoutMonth: 1
          },
          { payoutYear: 2025, payoutMonth: 4 },
          { payoutYear: 2025, payoutMonth: 7 },
          { payoutYear: 2025, payoutMonth: 10 }
        ],
        '2026': [
          {
            payoutYear: 2026,
            payoutMonth: 1,
            exclude: [
              {
                option: 'EXECUTIVE_MULTI_GEN_FLAT',
                code: 'HT'
              }
            ]
          },
          { payoutYear: 2026, payoutMonth: 4 },
          { payoutYear: 2026, payoutMonth: 7 },
          { payoutYear: 2026, payoutMonth: 10 }
        ],
        '2027': [
          {
            payoutYear: 2027,
            payoutMonth: 1,
            exclude: [
              {
                option: 'EXECUTIVE_MULTI_GEN_FLAT',
                code: 'HT'
              }
            ]
          },
          { payoutYear: 2027, payoutMonth: 4 },
          { payoutYear: 2027, payoutMonth: 7 },
          { payoutYear: 2027, payoutMonth: 10 }
        ]
      }
      expect(getHhPayoutPeriod(result, 15)).toEqual(payoutPeriod)
    })
  })

  describe('Voucher - Climate Vouchers', () => {
    it('should return correct payment amount for housing 1R', () => {
      const result = service.checkPayoutEligibility(HOUSING_1R)

      const payoutAmount = [
        { year: '2025', amount: 400, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 34)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 2R', () => {
      const result = service.checkPayoutEligibility(HOUSING_2R)

      const payoutAmount = [
        { year: '2025', amount: 400, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 34)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 3R', () => {
      const result = service.checkPayoutEligibility(HOUSING_3R)

      const payoutAmount = [
        { year: '2025', amount: 400, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 34)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 4R', () => {
      const result = service.checkPayoutEligibility(HOUSING_4R)

      const payoutAmount = [
        { year: '2025', amount: 400, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 34)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing 5R', () => {
      const result = service.checkPayoutEligibility(HOUSING_5R)

      const payoutAmount = [
        { year: '2025', amount: 400, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 34)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing exec', () => {
      const result = service.checkPayoutEligibility(HOUSING_EXEC)

      const payoutAmount = [
        { year: '2025', amount: 400, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 34)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for housing private', () => {
      const result = service.checkPayoutEligibility(HOUSING_PRIVATE)

      const payoutAmount = [
        { year: '2025', amount: 400, unit: 'currency', type: 'exact' }
      ]
      expect(getHhPayoutAmount(result, 34)).toEqual(payoutAmount)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(HOUSING_1R)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 4 }]
      }
      expect(getHhPayoutPeriod(result, 34)).toEqual(payoutPeriod)
    })
  })
})
