import { INestApplication } from '@nestjs/common'
import { Test } from '@nestjs/testing'
import _ from 'lodash'
import { AppValidationPipe } from 'src/app.pipe'
import request from 'supertest'

import { BCModule } from './bc.module'

const BC_SCHEMES_ENDPOINT_URL = '/sr/bc/benefits/check'

const MOCK_REQ_BODY = {
  householdMembers: [
    {
      yearOfBirth: 1925,
      assessableIncome: 'ASSESSABLE_INCOME_TIER_2',
      identity: 'You'
    },
    {
      yearOfBirth: 2022,
      assessableIncome: 'ASSESSABLE_INCOME_TIER_1',
      identity: 'Member 1',
      questionBear: 'Yes'
    },
    {
      yearOfBirth: 1955,
      assessableIncome: 'ASSESSABLE_INCOME_TIER_1',
      identity: 'Member 2',
      medisaveBalance: 'MEDISAVE_BALANCE_TIER_2'
    },
    {
      yearOfBirth: 1958,
      assessableIncome: 'ASSESSABLE_INCOME_TIER_1',
      identity: 'Member 3',
      medisaveBalance: 'MEDISAVE_BALANCE_TIER_2'
    }
  ],
  ownMoreThanOneProperty: 'No',
  propertyOwnership: 'OWNED_BY_ME_OR_HOUSEHOLD_MEMBER',
  housingType: 'PUBLIC_1R',
  annualValue: 'ANNUAL_VALUE_TIER_1',
  lang: 'en'
}

describe('BCController', () => {
  let app: INestApplication

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [BCModule]
    }).compile()

    app = moduleRef.createNestApplication()
    app.useGlobalPipes(new AppValidationPipe())

    await app.init()
  })

  afterAll(async () => {
    await app.close()
  })

  describe(`POST ${BC_SCHEMES_ENDPOINT_URL}`, () => {
    it('should return 400 with if any of the body properties are missing', async () => {
      for (let i = 0; i < Object.keys(MOCK_REQ_BODY).length; i++) {
        const key = Object.keys(MOCK_REQ_BODY)[i]
        const clonedMockReqBody = _.cloneDeep(MOCK_REQ_BODY)
        delete clonedMockReqBody[key]

        const res = await request(app.getHttpServer())
          .post(BC_SCHEMES_ENDPOINT_URL)
          .send(clonedMockReqBody)

        expect(res.statusCode).toEqual(400)
      }
    })

    it('should return 400 with if any of the householdMembers properties are missing', async () => {
      for (
        let i = 0;
        i < Object.keys(MOCK_REQ_BODY.householdMembers).length;
        i++
      ) {
        const key = Object.keys(MOCK_REQ_BODY.householdMembers)[i]
        const clonedMockReqBody = _.cloneDeep(MOCK_REQ_BODY)
        delete clonedMockReqBody.householdMembers[key]

        const res = await request(app.getHttpServer())
          .post(BC_SCHEMES_ENDPOINT_URL)
          .send(clonedMockReqBody)

        expect(res.statusCode).toEqual(400)
      }
    })

    it('should return 400 with if any of the body properties are invalid', async () => {
      for (let i = 0; i < Object.keys(MOCK_REQ_BODY).length; i++) {
        const key = Object.keys(MOCK_REQ_BODY)[i]
        const clonedMockReqBody = _.cloneDeep(MOCK_REQ_BODY)
        clonedMockReqBody[key] = { INVALID: { INVALID2: 'INVALID KEY' } }

        const res = await request(app.getHttpServer())
          .post(BC_SCHEMES_ENDPOINT_URL)
          .send(clonedMockReqBody)

        expect(res.statusCode).toEqual(400)
      }
    })

    it('should return 400 with if any of the householdMembers properties are invalid', async () => {
      for (
        let i = 0;
        i < Object.keys(MOCK_REQ_BODY.householdMembers).length;
        i++
      ) {
        const key = Object.keys(MOCK_REQ_BODY.householdMembers)[i]
        const clonedMockReqBody = _.cloneDeep(MOCK_REQ_BODY)
        clonedMockReqBody.householdMembers[key] = {
          INVALID: { INVALID2: 'INVALID KEY' }
        }

        const res = await request(app.getHttpServer())
          .post(BC_SCHEMES_ENDPOINT_URL)
          .send(clonedMockReqBody)

        expect(res.statusCode).toEqual(400)
      }
    })

    it('should return 201 with if all the body property are present and valid', async () => {
      const res = await request(app.getHttpServer())
        .post(BC_SCHEMES_ENDPOINT_URL)
        .send(MOCK_REQ_BODY)

      expect(res.statusCode).toEqual(201)
    })
  })
})
