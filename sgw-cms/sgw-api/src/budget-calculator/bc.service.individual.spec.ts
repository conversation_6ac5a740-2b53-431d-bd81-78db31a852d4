import { Test, TestingModule } from '@nestjs/testing'
import _ from 'lodash'
import { EligibilityHelper } from 'src/eligibility/eligibility.helper'

import { BCService } from './bc.service'
import { GetPayoutReq } from './dto/GetPayoutReq.dto'
import {
  GetPayoutRes,
  PayoutPeriod,
  YearsAndAmount
} from './dto/GetPayoutRes.dto'

const AGE_21_PROP_1_AV_1_AI_1: GetPayoutReq = {
  ownMoreThanOneProperty: 'No',
  propertyOwnership: 'OWNED_BY_ME_OR_HOUSEHOLD_MEMBER',
  housingType: 'PUBLIC_1R',
  annualValue: 'ANNUAL_VALUE_TIER_1',
  householdMembers: [
    {
      yearOfBirth: 2001,
      assessableIncome: 'ASSESSABLE_INCOME_TIER_1',
      questionBear: 'Yes',
      medisaveBalance: 'MEDISAVE_BALANCE_TIER_1',
      identity: 'You'
    }
  ],
  lang: 'en'
}

const AGE_21_PROP_2_AV_1_AI_1: GetPayoutReq = updateRequestData(
  AGE_21_PROP_1_AV_1_AI_1,
  'ownMoreThanOneProperty',
  'Yes'
)

const AGE_51_PROP_1_AV_1_AI_1: GetPayoutReq = updateRequestData(
  AGE_21_PROP_1_AV_1_AI_1,
  'householdMembers[0].yearOfBirth',
  1973
)

const AGE_55_PROP_1_AV_1_AI_1: GetPayoutReq = updateRequestData(
  AGE_21_PROP_1_AV_1_AI_1,
  'householdMembers[0].yearOfBirth',
  1967
)

const AGE_65_PROP_1_AV_1_AI_1: GetPayoutReq = updateRequestData(
  AGE_21_PROP_1_AV_1_AI_1,
  'householdMembers[0].yearOfBirth',
  1957
)

const AGE_75_PROP_1_AV_1_AI_1: GetPayoutReq = updateRequestData(
  AGE_21_PROP_1_AV_1_AI_1,
  'householdMembers[0].yearOfBirth',
  1947
)

const AGE_85_PROP_1_AV_1_AI_1: GetPayoutReq = updateRequestData(
  AGE_21_PROP_1_AV_1_AI_1,
  'householdMembers[0].yearOfBirth',
  1937
)

function updateRequestData(
  data: GetPayoutReq,
  path: string,
  value: string | number
): GetPayoutReq {
  return _.set(_.cloneDeep(data), path, value)
}

function getIndPayoutAmount(
  result: GetPayoutRes,
  schemeId: number
): YearsAndAmount[] {
  return _.find(result.individual, { schemeId })?.payout[0].yearsAndAmount
}

function getIndPayoutPeriod(
  result: GetPayoutRes,
  schemeId: number
): { [key: string]: PayoutPeriod[] } {
  return _.find(result.individual, { schemeId }).description
}

describe('BC Individual Schemes', () => {
  let service: BCService

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BCService, EligibilityHelper]
    }).compile()

    service = module.get<BCService>(BCService)
  })

  describe('Cash - Assurance Package (GST-CASH)', () => {
    it('should return correct payment amount for age >= 21, prop 1, AV 1, AI 1', () => {
      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 600, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 250, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 1)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 1, AV 1, AI 2', () => {
      const AGE_21_PROP_1_AV_1_AI_2: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_2'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_1_AI_2)

      const payoutAmount = [
        { year: '2025', amount: 600, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 250, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 1)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 1, AV 1, AI 3', () => {
      const AGE_21_PROP_1_AV_1_AI_3: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_3'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_1_AI_3)

      const payoutAmount = [
        { year: '2025', amount: 350, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 150, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 1)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 1, AV 1, AI 4', () => {
      const AGE_21_PROP_1_AV_1_AI_4: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_4'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_1_AI_4)

      const payoutAmount = [
        { year: '2025', amount: 100, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 100, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 1)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 2, AV 1, AI 1', () => {
      const result = service.checkPayoutEligibility(AGE_21_PROP_2_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 100, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 100, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 1)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 2, AV 1, AI 2', () => {
      const AGE_21_PROP_2_AV_1_AI_2: GetPayoutReq = updateRequestData(
        AGE_21_PROP_2_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_2'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_2_AV_1_AI_2)

      const payoutAmount = [
        { year: '2025', amount: 100, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 100, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 1)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 2, AV 1, AI 3', () => {
      const AGE_21_PROP_2_AV_1_AI_3: GetPayoutReq = updateRequestData(
        AGE_21_PROP_2_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_3'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_2_AV_1_AI_3)

      const payoutAmount = [
        { year: '2025', amount: 100, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 100, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 1)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 2, AV 1, AI 4', () => {
      const AGE_21_PROP_2_AV_1_AI_4: GetPayoutReq = updateRequestData(
        AGE_21_PROP_2_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_4'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_2_AV_1_AI_4)

      const payoutAmount = [
        { year: '2025', amount: 100, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 100, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 1)).toEqual(payoutAmount)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_1_AI_1)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 12 }],
        '2026': [{ payoutYear: 2026, payoutMonth: 12 }]
      }
      expect(getIndPayoutPeriod(result, 1)).toEqual(payoutPeriod)
    })
  })

  describe("Cash - Assurance Package Seniors' Bonus (GST-SENIORS)", () => {
    it('should return correct payment amount for age >= 55, prop 1, AV 1, AI 1', () => {
      const result = service.checkPayoutEligibility(AGE_55_PROP_1_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 250, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 2)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 55, prop 1, AV 2, AI 1', () => {
      const AGE_55_PROP_1_AV_2_AI_1: GetPayoutReq = updateRequestData(
        AGE_55_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_2'
      )

      const result = service.checkPayoutEligibility(AGE_55_PROP_1_AV_2_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 200, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 2)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 55, prop 1, AV 3, AI 1', () => {
      const AGE_55_PROP_1_AV_3_AI_1: GetPayoutReq = updateRequestData(
        AGE_55_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_3'
      )

      const result = service.checkPayoutEligibility(AGE_55_PROP_1_AV_3_AI_1)

      expect(getIndPayoutAmount(result, 2)).toEqual(undefined)
    })

    it('should return correct payment amount for age >= 65, prop 1, AV 1, AI 1', () => {
      const result = service.checkPayoutEligibility(AGE_65_PROP_1_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 300, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 2)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 65, prop 1, AV 2, AI 1', () => {
      const AGE_65_PROP_1_AV_2_AI_1: GetPayoutReq = updateRequestData(
        AGE_65_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_2'
      )

      const result = service.checkPayoutEligibility(AGE_65_PROP_1_AV_2_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 200, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 2)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 65, prop 1, AV 3, AI 1', () => {
      const AGE_65_PROP_1_AV_3_AI_1: GetPayoutReq = updateRequestData(
        AGE_65_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_3'
      )

      const result = service.checkPayoutEligibility(AGE_65_PROP_1_AV_3_AI_1)

      expect(getIndPayoutAmount(result, 2)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(AGE_55_PROP_1_AV_1_AI_1)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 2 }]
      }
      expect(getIndPayoutPeriod(result, 2)).toEqual(payoutPeriod)
    })
  })

  describe('Cash - GSTV (GSTV-CASH)', () => {
    it('should return correct payment amount for age >= 21, prop 1, AV 1, AI 1', () => {
      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 850, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 850, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 850, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 4)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 1, AV 1, AI 2', () => {
      const AGE_21_PROP_1_AV_1_AI_2: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_2'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_1_AI_2)

      const payoutAmount = [
        { year: '2025', amount: 850, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 850, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 850, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 4)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 1, AV 2, AI 1', () => {
      const AGE_21_PROP_1_AV_2_AI_1: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_2'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_2_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 450, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 450, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 450, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 4)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 1, AV 2, AI 2', () => {
      const AGE_21_PROP_1_AV_2_AI_1: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_2'
      )
      const AGE_21_PROP_1_AV_2_AI_2: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_2_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_2'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_2_AI_2)

      const payoutAmount = [
        { year: '2025', amount: 450, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 450, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 450, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 4)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 21, prop 1, AV 3, AI 1', () => {
      const AGE_21_PROP_1_AV_3_AI_1: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_3'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_3_AI_1)

      expect(getIndPayoutAmount(result, 4)).toEqual(undefined)
    })

    it('should return correct payment amount for age >= 21, prop 1, AV 3, AI 2', () => {
      const AGE_21_PROP_1_AV_3_AI_1: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_3'
      )

      const AGE_21_PROP_1_AV_3_AI_2: GetPayoutReq = updateRequestData(
        AGE_21_PROP_1_AV_3_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_2'
      )

      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_3_AI_2)

      expect(getIndPayoutAmount(result, 4)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(AGE_55_PROP_1_AV_1_AI_1)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 8 }],
        '2026': [{ payoutYear: 2026, payoutMonth: 8 }],
        '2027': [{ payoutYear: 2027, payoutMonth: 8 }]
      }
      expect(getIndPayoutPeriod(result, 4)).toEqual(payoutPeriod)
    })
  })

  describe('MediSave - Assurance Package (MEDISAVE-TOPUP)', () => {
    it('should return correct payment amount for age <= 20', () => {
      const AGE_20_PROP_1_AV_1_AI_1: GetPayoutReq = updateRequestData(
        AGE_55_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2020
      )
      const result = service.checkPayoutEligibility(AGE_20_PROP_1_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 150, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 5)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 55', () => {
      const result = service.checkPayoutEligibility(AGE_55_PROP_1_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 150, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 5)).toEqual(payoutAmount)
    })

    it('should not return payment amount for age >= 21 and < 55', () => {
      const result = service.checkPayoutEligibility(AGE_21_PROP_1_AV_1_AI_1)

      expect(getIndPayoutAmount(result, 5)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(AGE_55_PROP_1_AV_1_AI_1)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 2 }]
      }
      expect(getIndPayoutPeriod(result, 5)).toEqual(payoutPeriod)
    })
  })

  describe('MediSave - GSTV (GSTV-MEDISAVE)', () => {
    it('should return correct payment amount for age >= 65, prop 1, AV 1, AI 1', () => {
      const result = service.checkPayoutEligibility(AGE_65_PROP_1_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 250, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 250, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 250, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 6)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 65, prop 1, AV 2, AI 1', () => {
      const AGE_65_PROP_1_AV_2_AI_1: GetPayoutReq = updateRequestData(
        AGE_65_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_2'
      )
      const result = service.checkPayoutEligibility(AGE_65_PROP_1_AV_2_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 150, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 150, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 150, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 6)).toEqual(payoutAmount)
    })

    it('should not return payment amount for age >= 65, prop 1, AV 3, AI 1', () => {
      const AGE_65_PROP_1_AV_3_AI_1: GetPayoutReq = updateRequestData(
        AGE_65_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_3'
      )
      const result = service.checkPayoutEligibility(AGE_65_PROP_1_AV_3_AI_1)

      expect(getIndPayoutAmount(result, 6)).toEqual(undefined)
    })

    it('should return correct payment amount for age >= 75, prop 1, AV 1, AI 1', () => {
      const result = service.checkPayoutEligibility(AGE_75_PROP_1_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 350, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 350, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 350, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 6)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 75, prop 1, AV 2, AI 1', () => {
      const AGE_75_PROP_1_AV_2_AI_1: GetPayoutReq = updateRequestData(
        AGE_75_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_2'
      )
      const result = service.checkPayoutEligibility(AGE_75_PROP_1_AV_2_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 250, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 250, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 250, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 6)).toEqual(payoutAmount)
    })

    it('should not return payment amount for age >= 75, prop 1, AV 3, AI 1', () => {
      const AGE_75_PROP_1_AV_3_AI_1: GetPayoutReq = updateRequestData(
        AGE_75_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_3'
      )
      const result = service.checkPayoutEligibility(AGE_75_PROP_1_AV_3_AI_1)

      expect(getIndPayoutAmount(result, 6)).toEqual(undefined)
    })

    it('should return correct payment amount for age >= 85, prop 1, AV 1, AI 1', () => {
      const result = service.checkPayoutEligibility(AGE_85_PROP_1_AV_1_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 450, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 450, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 450, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 6)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 85, prop 1, AV 2, AI 1', () => {
      const AGE_85_PROP_1_AV_2_AI_1: GetPayoutReq = updateRequestData(
        AGE_85_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_2'
      )
      const result = service.checkPayoutEligibility(AGE_85_PROP_1_AV_2_AI_1)

      const payoutAmount = [
        { year: '2025', amount: 350, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 350, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 350, unit: 'currency', type: 'exact' }
      ]
      expect(getIndPayoutAmount(result, 6)).toEqual(payoutAmount)
    })

    it('should not return payment amount for age >= 85, prop 1, AV 3, AI 1', () => {
      const AGE_85_PROP_1_AV_3_AI_1: GetPayoutReq = updateRequestData(
        AGE_85_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_3'
      )
      const result = service.checkPayoutEligibility(AGE_85_PROP_1_AV_3_AI_1)

      expect(getIndPayoutAmount(result, 6)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(AGE_65_PROP_1_AV_1_AI_1)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 8 }],
        '2026': [{ payoutYear: 2026, payoutMonth: 8 }],
        '2027': [{ payoutYear: 2027, payoutMonth: 8 }]
      }
      expect(getIndPayoutPeriod(result, 6)).toEqual(payoutPeriod)
    })
  })

  describe('Majulah Package — Earn and Save Bonus', () => {
    it('should return correct payment amount for age >= 51, prop 1, AV 1, AI 1/2/3', () => {
      const AGE_51_PROP_1_AV_1_AI_2: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_2'
      )
      const AGE_51_PROP_1_AV_1_AI_3: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_3'
      )

      const ai1 = service.checkPayoutEligibility(AGE_51_PROP_1_AV_1_AI_1)
      const ai2 = service.checkPayoutEligibility(AGE_51_PROP_1_AV_1_AI_2)
      const ai3 = service.checkPayoutEligibility(AGE_51_PROP_1_AV_1_AI_3)

      const payoutAmount = [
        { year: '2025', amount: 1000, unit: 'currency', type: 'max' },
        { year: '2026', amount: 1000, unit: 'currency', type: 'max' },
        { year: '2027', amount: 1000, unit: 'currency', type: 'max' }
      ]
      expect(getIndPayoutAmount(ai1, 22)).toEqual(payoutAmount)
      expect(getIndPayoutAmount(ai2, 22)).toEqual(payoutAmount)
      expect(getIndPayoutAmount(ai3, 22)).toEqual(payoutAmount)
    })

    it('should return correct payment amount for age >= 51, prop 1, AV 2, AI 1/2/3', () => {
      const AGE_51_PROP_1_AV_2_AI_1: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_2'
      )
      const AGE_51_PROP_1_AV_2_AI_2: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_2_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_2'
      )

      const AGE_51_PROP_1_AV_2_AI_3: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_2_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_3'
      )

      const ai1 = service.checkPayoutEligibility(AGE_51_PROP_1_AV_2_AI_1)
      const ai2 = service.checkPayoutEligibility(AGE_51_PROP_1_AV_2_AI_2)
      const ai3 = service.checkPayoutEligibility(AGE_51_PROP_1_AV_2_AI_3)

      const payoutAmount = [
        { year: '2025', amount: 1000, unit: 'currency', type: 'max' },
        { year: '2026', amount: 1000, unit: 'currency', type: 'max' },
        { year: '2027', amount: 1000, unit: 'currency', type: 'max' }
      ]
      expect(getIndPayoutAmount(ai1, 22)).toEqual(payoutAmount)
      expect(getIndPayoutAmount(ai2, 22)).toEqual(payoutAmount)
      expect(getIndPayoutAmount(ai3, 22)).toEqual(payoutAmount)
    })

    it('should NOT return payment amount for age >= 51, prop 1, AV 1, AI 4', () => {
      const AGE_51_PROP_1_AV_1_AI_4: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_4'
      )

      const ai4 = service.checkPayoutEligibility(AGE_51_PROP_1_AV_1_AI_4)

      expect(getIndPayoutAmount(ai4, 22)).toEqual(undefined)
    })

    it('should NOT return payment amount for age >= 51, prop 1, AV 2, AI 4', () => {
      const AGE_51_PROP_1_AV_2_AI_1: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'annualValue',
        'ANNUAL_VALUE_TIER_2'
      )
      const AGE_51_PROP_1_AV_2_AI_4: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_2_AI_1,
        'householdMembers[0].assessableIncome',
        'ASSESSABLE_INCOME_TIER_4'
      )

      const ai4 = service.checkPayoutEligibility(AGE_51_PROP_1_AV_2_AI_4)

      expect(getIndPayoutAmount(ai4, 22)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const result = service.checkPayoutEligibility(AGE_51_PROP_1_AV_1_AI_1)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 3 }],
        '2026': [{ payoutYear: 2026, payoutMonth: 3 }],
        '2027': [{ payoutYear: 2027, payoutMonth: 3 }]
      }
      expect(getIndPayoutPeriod(result, 22)).toEqual(payoutPeriod)
    })
  })

  describe('CPF Top-up - Large Family MediSave Grant', () => {
    it('should return correct payout for age = 0, yes to question bear', () => {
      const AGE_0_QUESTION_BEAR_YES: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2025
      )
      const AGE_0_YES = service.checkPayoutEligibility(AGE_0_QUESTION_BEAR_YES)

      const payoutAmount = [
        { year: '2025', amount: 5000, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(AGE_0_YES, 23)).toEqual(payoutAmount)
    })

    it('should not return payout if age is not 0 in, 2025, 2026, 2027', () => {
      const AGE_1_QUESTION_BEAR_YES: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2024
      )

      const AGE_1_YES = service.checkPayoutEligibility(AGE_1_QUESTION_BEAR_YES)

      expect(getIndPayoutAmount(AGE_1_YES, 23)).toEqual(undefined)
    })

    it('should not return payout if age is 0 in 2025, 2026, 2027 but question bear is no', () => {
      const AGE_0_QUESTION_BEAR_YES: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2025
      )
      const AGE_0_QUESTION_BEAR_NO: GetPayoutReq = updateRequestData(
        AGE_0_QUESTION_BEAR_YES,
        'householdMembers[0].questionBear',
        'No'
      )

      const AGE_0_NO = service.checkPayoutEligibility(AGE_0_QUESTION_BEAR_NO)

      expect(getIndPayoutAmount(AGE_0_NO, 23)).toEqual(undefined)
    })
  })

  describe('Top-up - Increased Child Development Account First Step Grant', () => {
    it('should return correct payout for age = 0, yes to question bear', () => {
      const AGE_0_QUESTION_BEAR_YES: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2025
      )
      const AGE_0_YES = service.checkPayoutEligibility(AGE_0_QUESTION_BEAR_YES)

      const payoutAmount = [
        { year: '2025', amount: 10000, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(AGE_0_YES, 24)).toEqual(payoutAmount)
    })

    it('should not return payout if age is not 0 in, 2025, 2026, 2027', () => {
      const AGE_1_QUESTION_BEAR_YES: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2024
      )

      const AGE_1_YES = service.checkPayoutEligibility(AGE_1_QUESTION_BEAR_YES)

      expect(getIndPayoutAmount(AGE_1_YES, 24)).toEqual(undefined)
    })

    it('should not return payout if age is 0 in 2025, 2026, 2027 but question bear is no', () => {
      const AGE_0_QUESTION_BEAR_YES: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2025
      )
      const AGE_0_QUESTION_BEAR_NO: GetPayoutReq = updateRequestData(
        AGE_0_QUESTION_BEAR_YES,
        'householdMembers[0].questionBear',
        'No'
      )

      const AGE_0_NO = service.checkPayoutEligibility(AGE_0_QUESTION_BEAR_NO)

      expect(getIndPayoutAmount(AGE_0_NO, 24)).toEqual(undefined)
    })
  })

  describe('Credit - Large Family LifeSG Credits', () => {
    it('should return correct payout for age 1 to 6, yes to question bear', () => {
      const AGE_4_QUESTION_BEAR_YES: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2021
      )
      const AGE_4_YES = service.checkPayoutEligibility(AGE_4_QUESTION_BEAR_YES)

      const payoutAmount = [
        { year: '2025', amount: 1000, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 1000, unit: 'currency', type: 'exact' },
        { year: '2027', amount: 1000, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(AGE_4_YES, 25)).toEqual(payoutAmount)
    })

    it('should return payout only in 2025, 2026 if age in 2025 is 5', () => {
      const AGE_5_QUESTION_BEAR_YES: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2020
      )
      const payoutAmount = [
        { year: '2025', amount: 1000, unit: 'currency', type: 'exact' },
        { year: '2026', amount: 1000, unit: 'currency', type: 'exact' }
      ]

      const AGE_5_YES = service.checkPayoutEligibility(AGE_5_QUESTION_BEAR_YES)

      expect(getIndPayoutAmount(AGE_5_YES, 25)).toEqual(payoutAmount)
    })

    it('should not return payout if age is not 1 to 6 in, 2025, 2026, 2027', () => {
      const AGE_7_QUESTION_BEAR_YES: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2018
      )

      const AGE_7_YES = service.checkPayoutEligibility(AGE_7_QUESTION_BEAR_YES)

      expect(getIndPayoutAmount(AGE_7_YES, 25)).toEqual(undefined)
    })

    it('should not return payout if age is 3 in, 2025, 2026, 2027 but question bear is no', () => {
      const AGE_3: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2022
      )

      const AGE_3_QUESTION_BEAR_NO: GetPayoutReq = updateRequestData(
        AGE_3,
        'householdMembers[0].questionBear',
        'No'
      )

      const AGE_3_YES = service.checkPayoutEligibility(AGE_3_QUESTION_BEAR_NO)

      expect(getIndPayoutAmount(AGE_3_YES, 25)).toEqual(undefined)
    })
  })

  describe('Credit - Child LifeSG Credits', () => {
    it('should return correct payout for age 1 to 12', () => {
      const AGE_4: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2021
      )
      const AGE_4_YES = service.checkPayoutEligibility(AGE_4)

      const payoutAmount = [
        { year: '2025', amount: 500, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(AGE_4_YES, 26)).toEqual(payoutAmount)
    })

    it('should return correct payout for age === 0', () => {
      const AGE_0: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2025
      )
      const AGE_0_YES = service.checkPayoutEligibility(AGE_0)

      const payoutAmount = [
        { year: '2026', amount: 500, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(AGE_0_YES, 26)).toEqual(payoutAmount)
    })

    it('should NOT return payout if age is not 1 to 12', () => {
      const AGE_16: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2009
      )
      const AGE_16_YES = service.checkPayoutEligibility(AGE_16)

      expect(getIndPayoutAmount(AGE_16_YES, 26)).toEqual(undefined)
    })

    it('should return correct payout period for AGE 0', () => {
      const AGE_3: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2022
      )
      const result = service.checkPayoutEligibility(AGE_3)

      const payoutPeriod = {
        '2025': [
          {
            payoutYear: 2025,
            payoutMonth: 7
          }
        ],
        '2026': [{ payoutYear: 2026, payoutMonth: 4 }]
      }
      expect(getIndPayoutPeriod(result, 26)).toEqual(payoutPeriod)
    })

    it('should return correct payout period for >=4 <= 15', () => {
      const AGE_4: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2020
      )
      const AGE_4_YES = service.checkPayoutEligibility(AGE_4)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 7 }],
        '2026': [
          {
            payoutYear: 2026,
            payoutMonth: 4
          }
        ]
      }
      expect(getIndPayoutPeriod(AGE_4_YES, 26)).toEqual(payoutPeriod)
    })
  })

  describe('Top-up - Edusave Account', () => {
    it('should return correct payout for age >=13 <= 16', () => {
      const AGE_16: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2009
      )
      const AGE_16_YES = service.checkPayoutEligibility(AGE_16)

      const payoutAmount = [
        { year: '2025', amount: 500, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(AGE_16_YES, 27)).toEqual(payoutAmount)
    })

    it('should NOT return payout if age is not >=13 <= 16', () => {
      const AGE_16: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2005
      )
      const AGE_16_YES = service.checkPayoutEligibility(AGE_16)

      expect(getIndPayoutAmount(AGE_16_YES, 27)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const AGE_16: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2009
      )
      const result = service.checkPayoutEligibility(AGE_16)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 7 }]
      }
      expect(getIndPayoutPeriod(result, 27)).toEqual(payoutPeriod)
    })
  })

  describe('Top-up - Post-Secondary Education Account', () => {
    it('should return correct payout for age >=17 <= 20', () => {
      const AGE_20: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2005
      )
      const AGE_20_YES = service.checkPayoutEligibility(AGE_20)

      const payoutAmount = [
        { year: '2025', amount: 500, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(AGE_20_YES, 28)).toEqual(payoutAmount)
    })

    it('should NOT return payout if age is not 17 to 20', () => {
      const AGE_21: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2004
      )
      const AGE_21_YES = service.checkPayoutEligibility(AGE_21)

      expect(getIndPayoutAmount(AGE_21_YES, 28)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const AGE_20: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2005
      )
      const result = service.checkPayoutEligibility(AGE_20)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 7 }]
      }
      expect(getIndPayoutPeriod(result, 28)).toEqual(payoutPeriod)
    })
  })

  describe('Credit - SG Culture Pass Credits', () => {
    it('should return correct payout for age >=23', () => {
      const AGE_23: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2002
      )
      const AGE_23_YES = service.checkPayoutEligibility(AGE_23)

      const payoutAmount = [
        { year: '2025', amount: 100, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(AGE_23_YES, 29)).toEqual(payoutAmount)
    })

    it('should NOT return payout if age is not < 18', () => {
      const AGE_5: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2020
      )
      const AGE_5_RESULT = service.checkPayoutEligibility(AGE_5)

      expect(getIndPayoutAmount(AGE_5_RESULT, 29)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const AGE_23: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2002
      )
      const result = service.checkPayoutEligibility(AGE_23)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 9 }]
      }
      expect(getIndPayoutPeriod(result, 29)).toEqual(payoutPeriod)
    })
  })

  describe('Credit - SG60 ActiveSG Credit Top-Up', () => {
    it('should return correct payout for anyone', () => {
      const AGE_23: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2002
      )
      const result = service.checkPayoutEligibility(AGE_23)

      const payoutAmount = [
        { year: '2025', amount: 100, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(result, 30)).toEqual(payoutAmount)
    })

    it('should return correct payout period', () => {
      const AGE_23: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2002
      )
      const result = service.checkPayoutEligibility(AGE_23)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 6 }]
      }
      expect(getIndPayoutPeriod(result, 30)).toEqual(payoutPeriod)
    })
  })

  describe('Voucher - SG60 Vouchers', () => {
    it('should return correct payout for age >=21 <= 59', () => {
      const AGE_26: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        1999
      )
      const result = service.checkPayoutEligibility(AGE_26)

      const payoutAmount = [
        { year: '2025', amount: 600, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(result, 31)).toEqual(payoutAmount)
    })

    it('should return correct payout for age >=60', () => {
      const AGE_60: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        1965
      )
      const result = service.checkPayoutEligibility(AGE_60)

      const payoutAmount = [
        { year: '2025', amount: 800, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(result, 31)).toEqual(payoutAmount)
    })

    it('should NOT return payout if age is not < 21', () => {
      const AGE_20: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2005
      )
      const result = service.checkPayoutEligibility(AGE_20)

      expect(getIndPayoutAmount(result, 31)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const AGE_26: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        1999
      )
      const result = service.checkPayoutEligibility(AGE_26)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 7 }]
      }
      expect(getIndPayoutPeriod(result, 31)).toEqual(payoutPeriod)
    })
  })

  describe('Rebate - SG60 Personal Income Tax Rebate', () => {
    it('should return correct payout for age >= 21', () => {
      const AGE_26: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        1999
      )
      const result = service.checkPayoutEligibility(AGE_26)

      const payoutAmount = [
        { year: '2025', amount: 200, unit: 'currency', type: 'max' }
      ]

      expect(getIndPayoutAmount(result, 32)).toEqual(payoutAmount)
    })

    it('should NOT return payout if age is not >=21', () => {
      const AGE_20: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2005
      )
      const result = service.checkPayoutEligibility(AGE_20)

      expect(getIndPayoutAmount(result, 32)).toEqual(undefined)
    })
  })

  describe('CPF Top-up Additional MediSave Bonus', () => {
    it('should return correct payout for age >= 52 to 75', () => {
      const AGE_52: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        1973
      )
      const result = service.checkPayoutEligibility(AGE_52)

      const payoutAmount = [
        { year: '2025', amount: 500, unit: 'currency', type: 'exact' }
      ]

      expect(getIndPayoutAmount(result, 33)).toEqual(payoutAmount)
    })

    it('should NOT return payout if age is not 52 to 75', () => {
      const AGE_20: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        2005
      )
      const result = service.checkPayoutEligibility(AGE_20)

      expect(getIndPayoutAmount(result, 33)).toEqual(undefined)
    })

    it('should NOT return payout if age is 52 to 75 but Medisave balance is tier 2', () => {
      const AGE_20: GetPayoutReq = updateRequestData(
        AGE_55_PROP_1_AV_1_AI_1,
        'householdMembers[0].medisaveBalance',
        'MEDISAVE_BALANCE_TIER_2'
      )
      const result = service.checkPayoutEligibility(AGE_20)

      expect(getIndPayoutAmount(result, 33)).toEqual(undefined)
    })

    it('should NOT return payout if age is 52 to 75 but property is more than one', () => {
      const AGE_20: GetPayoutReq = updateRequestData(
        AGE_55_PROP_1_AV_1_AI_1,
        'ownMoreThanOneProperty',
        'Yes'
      )
      const result = service.checkPayoutEligibility(AGE_20)

      expect(getIndPayoutAmount(result, 33)).toEqual(undefined)
    })

    it('should return correct payout period', () => {
      const AGE_26: GetPayoutReq = updateRequestData(
        AGE_51_PROP_1_AV_1_AI_1,
        'householdMembers[0].yearOfBirth',
        1973
      )
      const result = service.checkPayoutEligibility(AGE_26)

      const payoutPeriod = {
        '2025': [{ payoutYear: 2025, payoutMonth: 7 }]
      }
      expect(getIndPayoutPeriod(result, 33)).toEqual(payoutPeriod)
    })
  })
})
