/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { INestApplication, VersioningType } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'
import { SwaggerModule } from '@nestjs/swagger'
import { LoggerService } from '@sgw/commons'

import { AppModule } from '../app.module'
import { AppValidationPipe } from '../app.pipe'
import { AllExceptionsFilter } from '../filters/all.exceptions.filter'
import { PrismaExceptionsFilter } from '../filters/prisma.exceptions.filter'
import { buildSwaggerConfig } from './swagger.config'

export class AppConfiguration {
  private static app: INestApplication

  static async init() {
    this.app = await NestFactory.create(AppModule, {
      bufferLogs: true
    })
    this.app.useLogger(this.app.get(LoggerService))
    this.app.useGlobalPipes(new AppValidationPipe())
    this.app.enableVersioning({
      defaultVersion: '1',
      type: VersioningType.URI
    })
    this.app.enableCors({
      origin: [
        'https://supportgowhere.life.gov.sg',
        'https://prd-2.supportgowhere.life.gov.sg',
        'https://prd-3.supportgowhere.life.gov.sg',
        'https://dev.supportgowhere.life.gov.sg',
        'https://dev-2.supportgowhere.life.gov.sg',
        'https://dev-3.supportgowhere.life.gov.sg',
        'https://dev-4.supportgowhere.life.gov.sg',
        'https://dev-5.supportgowhere.life.gov.sg',
        'https://stg.supportgowhere.life.gov.sg',
        'https://stg-2.supportgowhere.life.gov.sg',
        'https://stg-3.supportgowhere.life.gov.sg',
        'https://stg-4.supportgowhere.life.gov.sg',
        'https://uat.supportgowhere.life.gov.sg',
        'https://uat-2.supportgowhere.life.gov.sg',
        'https://sit.supportgowhere.life.gov.sg',
        'https://sit-2.supportgowhere.life.gov.sg'
      ]
    })
    this.app.getHttpAdapter().getInstance().disable('x-powered-by')
  }

  static enableSwagger() {
    const document = SwaggerModule.createDocument(
      this.app,
      buildSwaggerConfig()
    )
    SwaggerModule.setup('v1/sr/docs', this.app, document, {
      customCss: `.topbar-wrapper img {content:url('/v1/sr/assets/logo_with_name.svg'); width:150px; height:auto;}
                  .swagger-ui .topbar { background-color: #FAFAFA; }
                  .response-col_description div:nth-child(1)  ~ div:not(.model-example) { display: none; } `,
      customSiteTitle: 'API Documentation - SupportGoWhere API',
      customfavIcon: '/v1/sr/assets/favicon.ico',
      swaggerOptions: { defaultModelsExpandDepth: -1 }
    })
  }

  static enableGlobalFilters(): void {
    this.app.useGlobalFilters(
      new AllExceptionsFilter(
        process.env.ENV !== 'production' && process.env.ENV !== 'prod',
        this.app.get(LoggerService)
      )
    )
    this.app.useGlobalFilters(
      new PrismaExceptionsFilter(
        process.env.ENV !== 'production' && process.env.ENV !== 'prod'
      )
    )
  }

  static async start() {
    await this.app.listen(process.env.SERVER_PORT || 8081)
  }
}
