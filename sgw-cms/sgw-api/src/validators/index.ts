import { CategoryV2 } from '@sgw/database/prisma-sgw/utils/sb-utils'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import _ from 'lodash'

import { customValidator, TOptions, TValidator } from './utils'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(customParseFormat)
dayjs.tz.setDefault('Asia/Singapore')
export const VALID_VALUES = {
  MAX_AGE: 200,
  MIN_AGE: 0,
  MIN_HOUSEHOLD_SIZE: 1,
  MAX_HOUSEHOLD_SIZE: 99,
  MAX_HOUSEHOLD_INCOME: **********,
  MIN_HOUSEHOLD_INCOME: 0,
  MAX_CAPITA_INCOME: **********,
  MIN_CAPITA_INCOME: 0,
  MIN_HEALTHSTATUS_SIZE: 1,
  HEALTH_STATUS_VALUES: [
    'INDEPENDENT',
    'HOMEBOUND',
    'DISABLED_INTELLECTUAL',
    'DISABLED_PHYSICAL',
    'TERMINAL_ILLNESS',
    'DEPENDENT_ADL'
  ],
  ADL_VALUES: [
    'EATING',
    'SHOWERING',
    'MOVING_AROUND',
    'TOILETING',
    'WHEELCHAIR_ASSISTANCE',
    'CHANGING_CLOTHES'
  ],
  EMPLOYMENT_STATUS: [
    'EMPLOYED',
    'SELF_EMPLOYED',
    'UNEM_SEARCHING',
    'UNEM_NOT_SEARCHING',
    'STUDENT'
  ],
  RESIDENCE_TYPE: [
    'PUBLIC_1R_2R',
    'PUBLIC_3R_4R',
    'PUBLIC_5R',
    'PUBLIC_OTHER',
    'PRIVATE'
  ],
  OWNERSHIP_TYPE: [
    'YES',
    'NO_OWNED_BY_HOUSEHOLD',
    'NO_RENTED_FROM_HDB',
    'NO_RENTED_FROM_PRIVATE'
  ],
  CITIZENSHIP_VALUES: ['SG', 'PR', 'Other'],
  ALLOWED_LANGUAGES: ['en', 'zh', 'ms', 'ta'],
  ARCHETYPES: ['MSF_COMCARE_HOTLINE', 'CONTACT_CENTERS', 'PHYSICAL_CENTERS'],
  SERVICE_BUNDLES: [
    'LEGACY_PLANNING',
    'HOUSING_MONETISATION',
    'HEALTHCARE_ADEQUACY',
    'ACTIVE_AGEING',
    'HEALTHIERSG',
    'JOBS_AND_SKILLS',
    'HOME_CARE_SERVICES',
    'CARE_SUBSIDIES',
    'CAREGIVER_SUPPORT',
    'CAREGIVING_SUPPORT',
    'LIV',
    'ESSENTIAL_SERVICES',
    'FINANCIAL_ADEQUACY',
    'RETIREMENT',
    'CARE',
    'HEALTH_MANAGEMENT'
  ],
  CITIZENSHIPS: ['A', 'C', 'P'],
  FAMILY_CITIZENSHIPS: ['SPOUSE', 'PARENT', 'CHILD'],
  ANNUAL_VALUES_OF_PROPERTY: [
    // Old AV
    '13K_AND_BELOW',
    'ABOVE_13K_AND_LESS_THAN_21K',
    '21K_AND_ABOVE',

    // New AV
    '21K_AND_BELOW',
    'ABOVE_21K_AND_LESS_THAN_25K',
    '25K_AND_ABOVE',

    'OWN_MORE_THAN_1_PROPERTY'
  ],
  HEALTH_CONDITIONS: [
    'PERSON_WITH_DISABILITY',
    'REQUIRES_REHABILITATION',
    'DEMENTIA',
    'HOMEBOUND',
    'FRAIL'
  ],
  ACTIVITIES_OF_DAILY_LIVING: ['1', '2', '3_OR_MORE'],
  HAS_MENTAL_CAPACITY: ['YES', 'NO']
}

export const VALID_VALUES_BC = {
  OWN_MORE_THAN_ONE_PROPERTY: ['Yes', 'No'],
  HOUSING_TYPE: [
    'PUBLIC_1R',
    'PUBLIC_2R',
    'PUBLIC_3R',
    'PUBLIC_4R',
    'PUBLIC_5R',
    'EXECUTIVE_MULTI_GEN_FLAT',
    'PRIVATE_PROPERTY'
  ],
  OWNERSHIP_TYPE: [
    'OWNED_BY_ME_OR_HOUSEHOLD_MEMBER',
    'RENTED_FROM_HDB',
    'RENTED_FROM_OPEN_MARKET'
  ],
  ASSESSABLE_INCOME: [
    'ASSESSABLE_INCOME_TIER_1',
    'ASSESSABLE_INCOME_TIER_2',
    'ASSESSABLE_INCOME_TIER_3',
    'ASSESSABLE_INCOME_TIER_4'
  ],
  ANNUAL_VALUE: [
    'ANNUAL_VALUE_TIER_1',
    'ANNUAL_VALUE_TIER_2',
    'ANNUAL_VALUE_TIER_3'
  ],
  QUESTION_BEAR: ['Yes', 'No'],
  MEDISAVE_BALANCE: ['MEDISAVE_BALANCE_TIER_1', 'MEDISAVE_BALANCE_TIER_2']
}

export const VALID_VALUES_REDIRECT = {
  SUPPORT_TYPE: ['Scheme', 'Service'] as const,
  CONTENT_KEY_SCHEME: [
    'address',
    'agencyWebsite',
    'emailOrForm',
    'howToApply',
    'phoneNumber',
    'whatAreBenefits',
    'whoIsEligible'
  ] as const,
  CONTENT_KEY_SERVICE: [
    'aboutTheService',
    'howToApply',
    'whoIsEligible',
    'whatToExpect'
  ] as const
}

const ALL_VALID_VALUES_REDIRECT_CONTENT_KEY = [
  ...VALID_VALUES_REDIRECT.CONTENT_KEY_SCHEME,
  ...VALID_VALUES_REDIRECT.CONTENT_KEY_SERVICE
]

export type ContentKeyType =
  (typeof ALL_VALID_VALUES_REDIRECT_CONTENT_KEY)[number]

export const CategoriesInput: CategoryV2[] = [
  'CHILDREN_YOUTH',
  'COVID',
  'DISABILITY',
  'FINANCIAL',
  'HEALTHCARE',
  'HOUSING',
  'SENIORS',
  'WORK_LEARNING',
  'MENTAL_HEALTH',
  'FAMILIES_PARENTING',
  'BUDGET2022',
  'CAREGIVING'
]

export const ArrayIsInEnum = (
  items: string[],
  options?: TOptions
): TValidator => {
  return customValidator('ArrayIsInEnum', validateSubsetOfArray(items), options)
}

/* istanbul ignore next */
const validateSubsetOfArray =
  (items: string[]) =>
  (values: string[]): boolean => {
    return _.difference(values, items).length === 0
  }

export const transformCitizenship = (citizenship: string): string => {
  const CITIZENSHIP_MAPPING = {
    A: 'OTHERS',
    C: 'SG',
    P: 'PR'
  }
  return CITIZENSHIP_MAPPING[citizenship]
}

export const transformYOBToAge = (dob: string): number | undefined => {
  const date = dayjs(dob, 'YYYY-MM-DD')
  if (!date.isValid()) return undefined
  const now = dayjs()
  const age = now.year() - date.year()
  return age
}

export const transformDOBToAge = (dob: string): number | undefined => {
  const date = dayjs(dob, 'YYYY-MM-DD').startOf('day').tz('Asia/Singapore')
  if (!date.isValid()) return undefined
  const now = dayjs(dayjs(), 'YYYY-MM-DD').startOf('day').tz('Asia/Singapore')
  let age = now.year() - date.year()
  const isAfter = now.month() >= date.month() && now.date() >= date.date()
  const finalAge = isAfter ? age : --age
  return finalAge
}
