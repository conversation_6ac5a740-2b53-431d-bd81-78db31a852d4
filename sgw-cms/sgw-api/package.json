{"name": "@sgw/api", "version": "0.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "rm -rf dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "docker start sgw-database-pg && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "tsc --noEmit && eslint \"{src,apps,libs,test}/**/*.ts\" --fix --max-warnings=0", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:api": "NODE_TLS_REJECT_UNAUTHORIZED=0 jest --config ./jest.api.config.js"}, "dependencies": {"@aws-sdk/client-cloudwatch-logs": "^3.370.0", "@aws-sdk/client-sts": "^3.370.0", "@aws-sdk/credential-providers": "^3.370.0", "@azure/openai": "^1.0.0-beta.2", "@langchain/community": "^0.2.33", "@langchain/core": "^0.2.20", "@langchain/langgraph": "^0.2.20", "@langchain/openai": "^0.2.11", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^8.4.4", "@nestjs/config": "^2.0.0", "@nestjs/core": "^8.4.4", "@nestjs/platform-express": "^8.4.4", "@nestjs/serve-static": "^2.2.2", "@nestjs/swagger": "^5.2.1", "@nestjs/terminus": "^8.0.6", "@sgw/commons": "file:../sgw-commons", "@sgw/database": "file:../sgw-database", "@tiptap/core": "^2.5.8", "@tiptap/extension-image": "^2.5.8", "@tiptap/extension-link": "^2.5.8", "@tiptap/extension-table": "^2.5.8", "@tiptap/extension-table-cell": "^2.5.8", "@tiptap/extension-table-header": "^2.5.8", "@tiptap/extension-table-row": "^2.5.8", "@tiptap/extension-text-style": "^2.8.0", "@tiptap/extension-underline": "^2.5.8", "@tiptap/html": "^2.6.6", "@tiptap/starter-kit": "^2.5.8", "ai": "^3.1.3", "cache-manager": "^4.1.0", "class-transformer": "^0.5.1", "class-validator": "0.13.2-NexusIQ-patch", "connect-redis": "^6.1.3", "dayjs": "^1.11.9", "json-rules-engine": "^6.5.0", "jsonwebtoken": "^9.0.0", "langchain": "^0.2.20", "lodash": "^4.17.21", "morgan": "^1.10.0", "nanoid": "^3.3.8", "object-hash": "^2.2.0", "openai": "~4.4.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "swagger-ui-express": "^4.3.0", "winston": "^3.8.0", "winston-cloudwatch": "^6.2.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^3.22.4"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@nestjs/cli": "^8.2.5", "@nestjs/schedule": "^1.1.0", "@nestjs/schematics": "^8.0.10", "@nestjs/testing": "^8.2.6", "@types/connect-redis": "^0.0.18", "@types/express": "^4.17.7", "@types/gapi": "^0.0.44", "@types/jest": "^26.0.24", "@types/jsonwebtoken": "^9.0.1", "@types/lodash": "^4.14.178", "@types/mocha": "^9.1.0", "@types/morgan": "^1.9.1", "@types/node": "^14.0.27", "@types/pluralize": "0.0.29", "@types/supertest": "^2.0.10", "@typescript-eslint/eslint-plugin": "^3.8.0", "@typescript-eslint/parser": "^3.8.0", "aws-sdk-mock": "^5.6.2", "axios": "^1.8.3", "eslint": "^7.6.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-prettier": "^4.0.0", "googleapis": "^126.0.1", "husky": "^4.2.5", "jest": "^26.2.2", "jest-bamboo-reporter": "^1.2.1", "jest-html-reporter": "^3.5.0", "lint-staged": "^10.2.11", "node-mocks-http": "^1.11.0", "prettier": "^2.0.5", "rimraf": "^3.0.2", "supertest": "^4.0.2", "ts-jest": "26.1.4", "ts-loader": "^8.0.2", "ts-node": "^8.10.2", "tsconfig-paths": "^3.9.0", "typescript": "^4.5.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "resolutions": {"node-fetch": "2.6.9", "semver": "7.5.4", "jsonwebtoken/**/semver": "7.5.4", "multer": "2.0.0-rc.1", "dicer": "0.3.1", "@babel/traverse": "7.23.2", "axios": "1.8.3", "express": "4.21.2", "@nestjs/serve-static/path-to-regexp": "0.1.12", "@nestjs/swagger/path-to-regexp": "3.3.0", "@nestjs/platform-express/**/body-parser": "1.20.3", "json-rules-engine/jsonpath-plus": "10.0.0", "langchain": "^0.2.20", "@langchain/core": "^0.2.20", "expr-eval": "2.0.3-NexusIQ-patch", "nanoid": "3.3.8"}}