{"name": "@sgw/database", "version": "0.0.0", "description": "", "license": "UNLICENSED", "main": "dist/prisma/client/index.js", "scripts": {"build": "DISABLE_ERD=true prisma generate && DISABLE_ERD=true prisma generate --schema=./prisma-sgw/schema.prisma", "build:new": "DISABLE_ERD=true prisma generate", "db:start": "podman start sgw-database-pg", "db:migrate:dev": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:reset:force": "prisma migrate reset --force", "db:seed": "yarn db:seed:sgw && yarn db:seed:cg && yarn db:seed:accounts && yarn db:seed:permissions && yarn db:seed:update-criteria && yarn db:seed:taggings && yarn db:seed:group-roles && yarn db:script:add-merged-ec && yarn db:script:move-mos && yarn db:seed:nsh && yarn db:seed:newlisting && yarn db:populate:taggings-relations && yarn db:seed:cg:subdomain", "db:seed:accounts": "ts-node ../sgw-cms/data/seedAccounts.ts", "db:seed:sgw": "ts-node prisma-sgw/seed/seed.ts", "db:seed:cg": "ts-node prisma-sgw/seed/seed-cg.ts", "db:seed:nsh": "ts-node prisma-sgw/seed/nsh/seed-nsh.ts", "db:seed:update-nsh-1": "ts-node prisma-sgw/seed/nsh/update-nsh-1.ts", "db:seed:cg:subdomain": "ts-node prisma/scripts/seed-cg-2.0-subdomain/script.ts", "db:seed:taggings": "ts-node prisma/scripts/done/7-seed-tagging.ts && ts-node prisma/scripts/done/8-seed-tagging-keyword-disability.ts", "db:populate:taggings-relations": "ts-node prisma/scripts/done/9-populate-location-disability-relations.ts && ts-node prisma/scripts/done/10-populate-scheme-keyword-relations.ts && ts-node prisma/scripts/done/11-populate-service-keyword-relations.ts", "db:seed:permissions": "ts-node ../sgw-cms/data/seedPermissions.ts", "db:seed:group-roles": "ts-node ../sgw-cms/data/seedGroupRoles.ts", "db:seed:newlisting": "ts-node ../sgw-cms/data/seedNewListingPermission.ts", "db:seed:embeddings": "ts-node prisma/scripts/data/seed-embeddings.ts", "db:script:superadmin": "cd ../sgw-cms && yarn install && ts-node ./data/updateSuperadmin.ts", "db:script:move-mos": "ts-node prisma/scripts/done/3-combine-mos.ts", "db:script:add-merged-ec": "ts-node prisma/scripts/done/2-add-merged-eligibility.ts", "db:script:add-loc-grp": "ts-node prisma/scripts/add-location-group.ts", "db:script:update-loc-availability": "ts-node prisma/scripts/update-location-availability.ts", "db:script:additional-details-hotfix": "ts-node prisma/scripts/eligibility-hotfix.ts", "db:script:modified-location-cleanup": "ts-node prisma/scripts/modified-location-cleanup.ts", "db:script:transform-markdown-data": "ts-node prisma/scripts/transform-editor-data/index.ts", "db:reset:seed": "yarn db:reset && yarn db:seed", "db:create": "podman create --name sgw-database-pg -p 5432:5432 -e POSTGRES_USER=sgw-dev -e POSTGRES_PASSWORD=password -e POSTGRES_DB=sgw-backend pgvector/pgvector:pg13", "db:remove": "podman stop sgw-database-pg && docker rm sgw-database-pg", "db:seed:update-criteria": "ts-node prisma-sgw/seed/schemes/seedCriteria.ts", "db:update:svc-listing-admin": "ts-node prisma/scripts/update-service-listing-admin.ts", "db:update:sgo-sort-order": "ts-node prisma/scripts/update-sgo-data/update-sort-order.ts", "db:update:emps-h2w-schemes": "ts-node prisma/scripts/update-emps-h2w-schemes-non-prd.ts", "db:seed:sgo-subdomain": "ts-node prisma/scripts/update-sgo-data/seed-subdomain.ts", "db:seed:sgo-subsubdomain": "ts-node prisma/scripts/update-sgo-data/seed-subsubdomain.ts", "db:script:add-svc-owner": "ts-node prisma/scripts/add-service-service-owners.ts", "lint": "tsc --noEmit && eslint . --ext .ts,.tsx --max-warnings=0", "lint:fix": "tsc --noEmit && eslint . --ext .ts,.tsx --fix --plugin unused-imports --fix --rule 'unused-imports/no-unused-imports-ts: warn'", "test": "jest", "test:cov": "jest --coverage"}, "peerDependencies": {"@prisma/client": "^3.14.0"}, "devDependencies": {"@aws-sdk/client-opensearch": "^3.363.0", "@azure/openai": "^1.0.0-beta.2", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@faker-js/faker": "^7.3.0", "@mui/icons-material": "^5.16.5", "@mui/material": "^5.16.5", "@opensearch-project/opensearch": "^1.2.0", "@prisma/client": "4.12.0", "@tiptap/core": "^2.5.7", "@tiptap/extension-image": "^2.5.7", "@tiptap/extension-link": "^2.5.7", "@tiptap/extension-table": "^2.5.7", "@tiptap/extension-table-cell": "^2.5.7", "@tiptap/extension-table-header": "^2.5.7", "@tiptap/extension-table-row": "^2.5.7", "@tiptap/extension-underline": "^2.5.7", "@tiptap/html": "^2.5.7", "@tiptap/pm": "^2.5.7", "@tiptap/react": "^2.5.7", "@tiptap/starter-kit": "^2.5.7", "@types/aws4": "^1.11.3", "@types/jest": "^28.1.7", "@types/lodash": "^4.14.183", "@types/node": "^20.17.27", "@types/showdown": "^2.0.6", "@types/uuid": "^9.0.1", "aws4": "^1.12.0", "axios": "^1.6.8", "class-validator": "^0.13.2", "jest": "^28.1.3", "jest-html-reporter": "^3.6.0", "jest-mock-extended": "^2.0.7", "mui-tiptap": "^1.9.5", "react": "^18.3.1", "react-dom": "^18.3.1", "showdown": "^2.1.0", "ts-jest": "^28.0.8", "ts-node": "^10.9.1", "uuid": "^9.0.1", "xlsx": "^0.18.5", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-prettier": "^4.0.0", "prettier": "^2.0.5"}, "dependencies": {"prisma": "4.12.0"}, "resolutions": {"jest-html-reporter/**/semver": "6.3.1"}}