/**
 * This script is to update the scheme id of EMPS and H2W schemes
 */
import { PrismaClient } from '../../dist/prisma/client'

const prisma = new PrismaClient()

;(async (): Promise<void> => {
  await prisma.$transaction(async tx => {
    console.info(`>> [START] Updating friendlyId for EMPS and H2W schemes`)

    const sysAdmin = await tx.user.findFirstOrThrow({
      where: {
        providerFriendlyId: '<EMAIL>'
      }
    })

    const schemeUpdateDetails: {
      [key: string]: { oldFriendlyId: string; newFriendlyId: string }
    } = {
      EMPS: {
        oldFriendlyId: 'd7bBjISY',
        // dev: d7bBjISY
        // stg: F0DQXK1R
        // uat: LaqiK0a1
        newFriendlyId: 'ejdrPN9A'
      },
      H2W: {
        oldFriendlyId: 'nZtf7luZ',
        // dev: nZtf7luZ
        // stg: gjCLIOMs
        // uat: YXvaqIDk
        newFriendlyId: 'zQnhRFwq'
      }
    }

    // Loop through each listing and update the friendlyId
    for (let i = 0; i < Object.keys(schemeUpdateDetails).length; i++) {
      console.info(
        `>> Updating friendlyId for ${
          Object.keys(schemeUpdateDetails)[i]
        } scheme`
      )

      const key = Object.keys(schemeUpdateDetails)[i]
      const listingDetails = schemeUpdateDetails[key]
      const masterWhere = {
        friendlyId_contentType: {
          friendlyId: listingDetails.oldFriendlyId,
          contentType: 'MASTER' as const
        }
      }

      const draftWhere = {
        friendlyId_contentType: {
          friendlyId: listingDetails.oldFriendlyId,
          contentType: 'DRAFT' as const
        }
      }

      // Update master scheme
      const updatedMasterScheme = await tx.scheme.update({
        where: masterWhere,
        data: {
          friendlyId: listingDetails.newFriendlyId,
          updatedBy: sysAdmin.id
        }
      })
      console.info(
        `>> Updated friendlyId of ${updatedMasterScheme.title} from ${listingDetails.oldFriendlyId} to ${updatedMasterScheme.friendlyId} (master)`
      )
      // Update draft scheme (without modifying updatedBy)
      const existingDraft = await tx.scheme.findUnique({
        where: draftWhere
      })
      if (existingDraft) {
        const updatedDraftScheme = await tx.scheme.update({
          where: draftWhere,
          data: {
            friendlyId: listingDetails.newFriendlyId
          }
        })
        console.info(
          `>> Updated friendlyId of ${updatedDraftScheme.title} from ${listingDetails.oldFriendlyId} to ${updatedDraftScheme.friendlyId} (draft)`
        )
      } else {
        console.info(
          `>> No drafts found for ${Object.keys(schemeUpdateDetails)[i]}`
        )
      }
    }

    console.info(`>> [END] Updating friendlyId for EMPS and H2W schemes`)
  })
})()
  .catch(e => {
    console.info(`>> [ERROR] occured while updated schemes`, e)
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
