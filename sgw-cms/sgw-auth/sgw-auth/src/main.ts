// eslint-disable-next-line @typescript-eslint/no-var-requires

import { SessionProvider } from './server/session/session.provider'

require('dotenv').config()

import { ValidationPipe } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { HttpAdapterHost, NestFactory } from '@nestjs/core'
import { NestExpressApplication } from '@nestjs/platform-express'
import { LoggerService } from '@sgw/commons'
import * as cons from 'consolidate'
import cookieParser from 'cookie-parser'
import { join } from 'node:path'

import { AppModule } from './app.module'
import { ClassValidationError } from './exceptions/ClassValidationError'
import { Config } from './server/config/config'
import { AllExceptionsFilter } from './server/filters/exceptions.filter'

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bufferLogs: true
  })
  const config: ConfigService<Config, true> = app.get(ConfigService)
  const session: SessionProvider = app.get(SessionProvider)
  const httpAdapterHost = app.get(HttpAdapterHost)

  app.enableCors()
  app.use(session.instance)
  app.use(cookieParser())

  app.useLogger(app.get(LoggerService))
  app.useGlobalPipes(
    new ValidationPipe({
      exceptionFactory: errors => new ClassValidationError(errors)
    })
  )
  app.useGlobalFilters(
    new AllExceptionsFilter(
      httpAdapterHost,
      config as any as ConfigService<Config>
    )
  )

  app.useStaticAssets(join(__dirname, 'public'), { prefix: '/auth/static' })
  app.setBaseViewsDir(join(__dirname, 'src/views'))
  app.engine('mustache', cons.mustache)
  app.setViewEngine('mustache')

  await app.listen(config.get('PORT'))
  console.info('App has started on port: ', config.get('PORT'))
}
bootstrap()
